# whatsapp-python Repository Documentation

Generated on: 2025-08-29 11:57:32 UTC
Repository: https://github.com/filipporomani/whatsapp-python
Description: Free, open-source Python wrapper for the WhatsApp Cloud API

This file contains comprehensive documentation and source code for the whatsapp-python library,
automatically generated using Crawl4AI for LLM consumption and understanding.

## Overview

whatsapp-python is a modern Python library that provides:
- Async/await interface for WhatsApp Cloud API
- Full support for Graph API error handling
- Optimized for high-load workflows using asynchronous programming
- All WhatsApp Business chat UI features supported
- Event listening for incoming messages
- Media sending (images, audio, video, documents)
- Interactive buttons and templates
- Message reactions and replies
- Location sharing
- Contact management
- Template message support
- Webhook integration

## Key Features
- Modern interface using async and await
- Always up to date with WhatsApp API changes
- Forked from Neurotech-HQ/heyoo with significant improvements
- Comprehensive error handling
- Extensive documentation and examples

# Wiki Home Page
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Findex&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Home
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Home/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Apr 8, 2023 · [4 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Home/_history)
Welcome to the whatsapp-python wiki!
Here you can find the documentation for the whatsapp-python library.
## Getting started
[](https://github.com/filipporomani/whatsapp-python/wiki#getting-started)
To get started with **whatsapp-python** , you need to install it first. You can install it from pip or from source. View the [Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup) page for more information.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
    * [Getting started](https://github.com/filipporomani/whatsapp-python/wiki#getting-started)
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Installation & Setup
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FInstallation-%26-Setup)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FInstallation-%26-Setup)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Installation & Setup
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Installation-%26-Setup/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Oct 13, 2024 · [8 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup/_history)
### Installing from source - always up to date version
[](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#installing-from-source---always-up-to-date-version)
Please note that this version is not stable and may contain bugs. If you want to use a stable version, please install from pip.
```
git clone https://github.com/filipporomani/whatsapp
cd whatsapp
pip install .
```

## Installing from pip - stable version
[](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#installing-from-pip---stable-version)
```
# For Windows 

pip install --upgrade whatsapp-python

#For Linux | MAC 

pip3 install --upgrade whatsapp-python
```

### Setting up
[](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#setting-up)
To get started using this package, you will need **TOKEN** and **TEST WHATSAPP NUMBER** (the library works either with a production phone number, if you have one) which you can get from the [Facebook Developer Portal](https://developers.facebook.com/)
Here are steps to follow for you to get started:
  1. [Go to your apps](https://developers.facebook.com/apps)
  2. [create an app](https://developers.facebook.com/apps/create/)
  3. Select Business >> Business
  4. It will prompt you to enter basic app informations
  5. It will ask you to add products to your app 
     * Add WhatsApp Messenger
  6. Right there you will see a your **TOKEN** and **TEST WHATSAPP NUMBER** and its phone_number_id
  7. Lastly verify the number you will be using for testing on the **To** field.


Once you've followed the above procedures you're ready to start using the package.
### Authentication
[](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#authentication)
To authenticate your application, you need to specify the `TOKEN` and the `phone_number_id` of your application.
The `logger` parameter is optional and it's used to disable logging (default: `True`)
The `update_check` parameter is optional and it's used to disable the update checker (default - recommended: `True`)
The `debug` arguments enables debug logging. By default, this is set to False.
The `version` argument specifies a custom version number of the Graph API. By default, the last available version is used.
```
>>> from whatsapp import WhatsApp, Message
>>> messenger = WhatsApp('TOKEN',  phone_number_id={"key": 'xxxxxxxxx', "key1": 'yyyyyyyyy'}, logger=True, update_check=True, debug=False, version="latest")
```

Starting from versions > 3.2.5 you can use different phone numbers to send messages. The phone_number_id parameter is a dictionary with the phone number as the key and the phone number id as the value. When sending a message, you'll be able to choose the phone number to send the message from. Please refer to the `Sending messages` section for more information. Starting from versions > 3.4.0 you won't be able to init the WhatsApp object using a string as the phone_number_id parameter. You'll need to use a dictionary instead.
Once you have authenticated your app you can start using the above mentioned feature as shown above;
> It is only possible to send messages other than templates only after the target phone responds to an initial template message or sends a message first. This resets every 24 hours; after that, you need to send a template again or the message won't be delivered. Reference: <https://developers.facebook.com/community/threads/425605939396247/>
### Logging
[](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#logging)
You can configure your own log level. This is an example to set the log level to info. By default only Error messages are logged.
```
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
```

To disable logging, set the `logger` parameter to `False` when initializing the `WhatsApp` object. To disable debug messages, set the `debug` parameter to `False` when initializing the `WhatsApp` object.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
    * [Installing from source - always up to date version](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#installing-from-source---always-up-to-date-version)
    * [Installing from pip - stable version](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#installing-from-pip---stable-version)
    * [Setting up](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#setting-up)
    * [Authentication](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#authentication)
    * [Logging](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup#logging)
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: App events
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/App-events#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FApp-events)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FApp-events)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/App-events) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/App-events) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/App-events) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# App events
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/App-events#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/App-events/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [5 revisions](https://github.com/filipporomani/whatsapp-python/wiki/App-events/_history)
Starting from version 2.9.2 you can listen to events.
The current supported events are:
  * on_message, called on every message received
  * on_verification, called once the app gets a verification request from Facebook
  * on_event, called every time a message arrives or a verification request is received.


All the event listeners **must** be asynchronous for any version > 2.9.3.
To create an event listener app, you need to create a WhatsApp() instance and pass it a callback function. The callback function will be called every time an event occurs.
```
from whatsapp import WhatsApp

app = WhatsApp(token, {"key": phone_number_id}, logger) # reference: Installation & setup

@app.on_message
async def on_message(message):
    print(message)

app.run(host, port, **kwargs)
```

The on_message event is triggered on a new message in any chat. The Message object is passed as an argument to the callback function.
The on_verification event is triggered when the webhook gets verified by WhatsApp. The Verification object is passed as an argument to the callback function. If the verification is not successful, the callback function will return False.
The on_event event is triggered on any event. A single argument is passed, whose type depends on the event type (Message, str, bool, etc).
### You can always use the WhatsApp object to send/parse messages. Listening to events is optional.
[](https://github.com/filipporomani/whatsapp-python/wiki/App-events#you-can-always-use-the-whatsapp-object-to-sendparse-messages-listening-to-events-is-optional)
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
    * [You can always use the WhatsApp object to send/parse messages. Listening to events is optional.](https://github.com/filipporomani/whatsapp-python/wiki/App-events#you-can-always-use-the-whatsapp-object-to-sendparse-messages-listening-to-events-is-optional)
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/App-events).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Async
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Async#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FAsync)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FAsync)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Async) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Async) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Async) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Async
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Async#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Async/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Omigirl edited this page Dec 5, 2024 · [4 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Async/_history)
If you want to use the async version of the library, you can use the `AsyncWhatsApp` class. This class is a different implementation of the `WhatsApp` class, and it's based on the `asyncio` library. This means that you can use the `await` keyword to call the methods of the class. You must also use the `AsyncMessage` class instead of the `Message` class.
The key benefit of using the async version is that you can send multiple messages at the same time without blocking the main thread - but this can also lead to some issues if you're not careful.
Here's an example of how to use the async version of the library:
```
from whatsapp import AsyncWhatsApp, AsyncMessage
import asyncio

messenger = AsyncWhatsApp('TOKEN',  phone_number_id={"key": 'xxxxxxxxx', "key1": 'yyyyyyyyy'}, logger=True, update_check=True, debug=False, version="latest")

msg = app.create_message(to="DESTINATION_PHONE_NUMBER", content="Hello world") # the create message function is not awaited as it doesn't call any API




async def main():
    # we send the message and the call is saved for later as .send() doesn't wait for the request to be finished.
    msg = await msg.send()

    # Arbitrary 5 second sleep time so we are sure the request is closed - a while cycle can also be used.
    await asyncio.sleep(5)

    # check if the message request is closed and print the result in the terminal
    if msg.done():
        print(f"Result: {msg.result()}")

asyncio.run(main())
```

In this example, we define an `async` function called `send_message` that sends a message using the `AsyncWhatsApp` class. We then define a `main` function that calls the `send_message` function and waits for the message to be sent. Finally, we use the `asyncio.run` function to run the `main` function.
The main issue with using the async version of the library is that you need to be careful when sending multiple messages at the same time. If you send too many messages too quickly, you may run into rate limiting issues or messages might be delivered in the wrong order. To avoid these issues, you should use the `asyncio` library to manage the sending of messages and make sure that you're not sending too many messages at once.
You should send messages in a controlled way, and make sure that you're not sending too many messages at once. You should also make sure that you're handling any errors that occur when sending messages, and that you're not blocking the main thread while sending messages.
Depending on the code you are building, you might also need to add a waiting time before exiting the software to allow all the tasks to complete.
The rest of the documentation is the same as the sync version of the library, so you can refer to all the other pages! However, when trying to send messages or to query the API, you must await every method call.
If you have any questions or issues, feel free to open an issue as this is a new feature and there might be some bugs that I haven't caught yet. I'll be happy to help you out!
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Async).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Error handling
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FError-handling)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FError-handling)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Error handling
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Nov 25, 2024 · [1 revision](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling/_history)
# Using the `sync` version (a.k.a. `WhatsApp()` class)
[](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#using-the-sync-version-aka-whatsapp-class)
Errors are already handled while using the synchronous version of the library. While sending a message or a button (or anything else), you can make a try-except statement to catch any eventual error:
```
... 

print("sending button")
try:
    app.send_button(
        {
            "header": "Header Testing", # obviously wrong button, as way more content is needed
            
        },
        dest_phone_number,
        sender=1,
    )
except Exception as e:
    print(f"send_button: {e}")

...
```

This will return the following text in the terminal:
`send_button: {'error': "(#100) The parameter interactive['action'] is required.", 'code': 100}`
The error handler is implemented by default in every library method.
# Using the `async` version (a.k.a. `AsyncWhatsApp`)
[](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#using-the-async-version-aka-asyncwhatsapp)
Errors must be handled manually one by one while using the asynchronous version of the library as the request is going to be completed in the future. While sending a message or a button (or anything else), you can handle any error as follows:
```
... 

print("sending button")
v = await app.send_button(
    {
        "header": "Header Testing", # obviously wrong button, as way more content is needed
        
    },
    dest_phone_number,
    sender=1,
)
while not v.done():
    await asyncio.sleep(1) # wait for the request to finish
try: app.handle(v.result())
except Exception as error:  
    print(f"error: {error}") # catch the error
...
```

This will return the following text in the terminal:
`send_button: {'error': "(#100) The parameter interactive['action'] is required.", 'code': 100}`
The `handle(data: dict)` function is built to take the request json output and parse the error if it contains one, raising an Exception with the error code and message.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
    * [Using the sync version (a.k.a. WhatsApp() class)](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#using-the-sync-version-aka-whatsapp-class)
    * [Using the async version (a.k.a. AsyncWhatsApp)](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling#using-the-async-version-aka-asyncwhatsapp)
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending messages
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-messages)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-messages)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending messages
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Feb 24, 2025 · [1 revision](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages/_history)
Use this method to send text message to a WhatsApp number. UPDATE: Message is now and object of Message class.
```
message = Message(instance=yourclient, content="Hello world!", to="00123456789") # this is your message instance, sender is the phone number key you want to use
message.send(sender="key") # this will send the message
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending images
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-images)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-images)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending images
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [3 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images/_history)
When sending media(image, video, audio, gif and document ), you can either specify a link containing the media or specify object id, you can do this using the same method.
By default all media methods assume you're sending link containing media but you can change this by specifying the `link=False`.
Here an example;
```
>>> messenger.send_image(
        image="https://i.imgur.com/Fh7XVYY.jpeg",
        recipient_id="255757xxxxxx",
        sender=0,
    )
```

> Note: You can also send media from your local machine but you have to upload it first to Whatsapp Cloud API, you can do this using the `upload_media` method. and then use the returned object id to send the media.
Here an example;
```
>>> media_id = messenger.upload_media(
        media='path/to/media',
    )['id']
>>> messenger.send_image(
        image=media_id,
        recipient_id="255757xxxxxx",
        link=False
        sender=0,
    )
```

> Note: Don't forget to set the link to False, and also you can use the same technique for sending video, audio, gif and document from your local machine.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending audios
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-audios)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-audios)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending audios
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [3 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios/_history)
To send a audio, you can either specify a link containing the audio or specify object id, you can do this using the same method. To get the object id, you can use the `upload_media` method. and then use the returned object id to send the audio.
```
>>> messenger.send_audio(
        audio="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
        recipient_id="255757xxxxxx",
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending videos
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-videos)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-videos)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending videos
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos/_history)
To send a video, you can either specify a link containing the video or specify object id, you can do this using the same method. To get the object id, you can use the `upload_media` method. and then use the returned object id to send the video.
```
>>> messenger.send_video(
        video="https://www.youtube.com/watch?v=K4TOrB7at0Y",
        recipient_id="255757xxxxxx",
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending documents
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-documents)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-documents)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending documents
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [3 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents/_history)
To send a document, you can either specify a link containing the document or specify object id, you can do this using the same method. To get the object id, you can use the `upload_media` method. and then use the returned object id to send the document.
```
>>> messenger.send_document(
        document="http://www.africau.edu/images/default/sample.pdf",
        recipient_id="255757xxxxxx",
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending location
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-location)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-location)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending location
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location/_history)
To send a location you can use the `send_location` method. You can specify the latitude, longitude, name, address and the recipient id.
```
>>> messenger.send_location(
        lat=1.29,
        long=103.85,
        name="Singapore",
        address="Singapore",
        recipient_id="255757xxxxxx",
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending interactive button (list)
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-interactive-button-%28list%29)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-interactive-button-%28list%29)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending interactive button (list)
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-%28list%29/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)/_history)
> Note: row button title may not exceed 20 characters otherwise your message will not be sent to the target phone.
This functions sends a button to toggle a list select element. Reference: [Facebook API](https://developers.facebook.com/docs/whatsapp/guides/interactive-messages/)
```
>>> messenger.send_button(
        recipient_id="255757xxxxxx",
        button={
            "header": "Header Testing",
            "body": "Body Testing",
            "footer": "Footer Testing",
            "action": {
                "button": "Button Testing",
                "sections": [
                    {
                        "title": "iBank",
                        "rows": [
                            {"id": "row 1", "title": "Send Money", "description": ""},
                            {
                                "id": "row 2",
                                "title": "Withdraw money",
                                "description": "",
                            },
                        ],
                    }
                ],
            },
        },
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\)).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending reply buttons
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-reply-buttons)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-reply-buttons)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending reply buttons
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons/_history)
> Send reply button only displays three reply buttons, if it exceeds three reply buttons, it will raise an error and your message will not be sent.
```
>>> messenger.send_reply_button(
        recipient_id="255757xxxxxx",
        button={
            "type": "button",
            "body": {
                "text": "This is a test button"
            },
            "action": {
                "buttons": [
                    {
                        "type": "reply",
                        "reply": {
                            "id": "b1",
                            "title": "This is button 1"
                        }
                    },
                    {
                        "type": "reply",
                        "reply": {
                            "id": "b2",
                            "title": "this is button 2"
                        }
                    }
                ]
            }
      },
        sender=0,
    )
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Sending templates
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-templates)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FSending-templates)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Sending templates
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates/_history)
A Template messages can either be:
  1. Text template
  2. Media based template
  3. Interactive template


You can customize the template message by passing a dictionary of components.
IMPORTANT: components are also known as variable parameters (like `{{0}}` or `{{1}}`) which are used to include variables into a message. You can find the available components in the documentation. <https://developers.facebook.com/docs/whatsapp/cloud-api/guides/send-message-templates>
```
>>> messenger.send_template("hello_world", "255757xxxxxx", components=[], lang="en_US", sender=0,)
```

`lang` is optional but required when sending templates in other languages.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Message() Object
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FMessage%28%29-Object)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FMessage%28%29-Object)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Message() Object
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Message%28%29-Object/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Apr 15, 2023 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object/_history)
You can use the `Message` object to easily interact with messages. For example, you can reply to a message using the `reply` method.
```
message = Message(instance=yourclient, id="MESSAGEID") # this is your message instance
message.reply("Hello world!")
```

## API Reference
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#api-reference)
### Message
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#message)
#### Message()
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#message-1)
You can also create a message object using the `WhatsApp` class. This method doesn't require to pass the `instance` parameter.
```
message = yourclient.create_message(args) # args are described below
```

Create a new message object.
##### Parameters
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#parameters)
  * `instance` **WhatsApp object**
  * `id` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**
  * `data` **[dict](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)**
  * `content` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**
  * `to` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**
  * `rec_type` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)**


#### Message.reply(content)
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagereplycontent)
Reply to a message. Required Message() initialization parameters:
  * instance
  * id


#### Message.send()
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagesend)
Send a message. Required Message() initialization parameters:
  * instance
  * to
  * content


#### Message.mark_as_read()
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagemark_as_read)
Mark a message as read. Required Message() initialization parameters:
  * instance
  * id


#### Properties
[](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#properties)
  * `Message().image` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The image URL of the message.
  * `Message().video` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The video URL of the message.
  * `Message().audio` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The audio URL of the message.
  * `Message().document` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The document URL of the message.
  * `Message().location` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The location of the message.
  * `Message().interactive` **[str](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** - The interactive reply of the message.


Each of these properties will return `None` if the message does not contain the specified content.
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
    * [API Reference](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#api-reference)
    * [Message](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#message)
    * [Message()](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#message-1)
    * [Parameters](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#parameters)
    * [Message.reply(content)](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagereplycontent)
    * [Message.send()](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagesend)
    * [Message.mark_as_read()](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#messagemark_as_read)
    * [Properties](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object#properties)
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Marking messages as read
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FMarking-messages-as-read)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FMarking-messages-as-read)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Marking messages as read
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Apr 8, 2023 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read/_history)
Use this method to mark a previously sent text message as read.
```
msg = Message(instance=yourclient, id="MESSAGEID")
msg.mark_as_read()
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Replying to message
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FReplying-to-message)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FReplying-to-message)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Replying to message
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Apr 8, 2023 · [1 revision](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message/_history)
Use this method to reply to a text message.
```
message = Message(instance=yourclient, id="MESSAGEID") # this is your message instance
message.reply("Hello world!")
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Reacting to messages
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FReacting-to-messages)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FReacting-to-messages)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Reacting to messages
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Sep 13, 2024 · [2 revisions](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages/_history)
Use this method to mark a previously sent text message as read.
```
app = WhatsApp(token, phone_number_id = {"key": "xxxxxxxxx"})


@app.on_message
async def on_message(message):
    message.react("👍")

app.run("0.0.0.0", 8080)
```

[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages).
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Wiki: Webhook & Heroku
[Skip to content](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FWebhook-%26-Heroku)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Fwiki%2FWebhook-%26-Heroku)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


# Webhook & Heroku
[Jump to bottom](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku#wiki-pages-box) [ Edit ](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-%26-Heroku/_edit) [ New page ](https://github.com/filipporomani/whatsapp-python/wiki/_new)
Filippo Romani edited this page Nov 23, 2024 · [1 revision](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku/_history)
Webhook are useful incase you're wondering how to respond to incoming message sent by users. An example of webhook is shown in the [hook.py](https://github.com/filipporomani/whatsapp-python/wiki/hook/hook.py) file.
PLEASE NOTE: the webhook requires other libraries to work, all of them are however included in the whatsapp package and should be already installed.
To learn more about webhook and how to configure in your Facebook developer dashboard please [have a look here](https://developers.facebook.com/docs/whatsapp/cloud-api/guides/set-up-webhooks).
[ ](https://github.com/filipporomani/whatsapp-python/wiki/_Footer/_edit)
If you find any issue in the docs, please [open an issue/pr and report it](https://github.com/filipporomani/whatsapp/issues)
###  Toggle table of contents Pages 20
  * Loading
[Home](https://github.com/filipporomani/whatsapp-python/wiki)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[App events](https://github.com/filipporomani/whatsapp-python/wiki/App-events)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Async](https://github.com/filipporomani/whatsapp-python/wiki/Async)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Error handling](https://github.com/filipporomani/whatsapp-python/wiki/Error-handling)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Installation & Setup](https://github.com/filipporomani/whatsapp-python/wiki/Installation-&-Setup)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Marking messages as read](https://github.com/filipporomani/whatsapp-python/wiki/Marking-messages-as-read)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Message() Object](https://github.com/filipporomani/whatsapp-python/wiki/Message\(\)-Object)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Reacting to messages](https://github.com/filipporomani/whatsapp-python/wiki/Reacting-to-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Replying to message](https://github.com/filipporomani/whatsapp-python/wiki/Replying-to-message)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending audios](https://github.com/filipporomani/whatsapp-python/wiki/Sending-audios)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending documents](https://github.com/filipporomani/whatsapp-python/wiki/Sending-documents)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending images](https://github.com/filipporomani/whatsapp-python/wiki/Sending-images)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending interactive button (list)](https://github.com/filipporomani/whatsapp-python/wiki/Sending-interactive-button-\(list\))
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending location](https://github.com/filipporomani/whatsapp-python/wiki/Sending-location)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending messages](https://github.com/filipporomani/whatsapp-python/wiki/Sending-messages)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending reply buttons](https://github.com/filipporomani/whatsapp-python/wiki/Sending-reply-buttons)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending templates](https://github.com/filipporomani/whatsapp-python/wiki/Sending-templates)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Sending videos](https://github.com/filipporomani/whatsapp-python/wiki/Sending-videos)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[v1.1.2](https://github.com/filipporomani/whatsapp-python/wiki/v1.1.2)
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku).
  * Loading
[Webhook & Heroku](https://github.com/filipporomani/whatsapp-python/wiki/Webhook-&-Heroku)
  * Show 5 more pages… 


[ ](https://github.com/filipporomani/whatsapp-python/wiki/_new?wiki%5Bname%5D=_Sidebar)
##### Clone this wiki locally
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## File: README.md
```
<div align="center">
  <img src="https://gist.githubusercontent.com/boywithkeyboard/e8dc5b1810bd29e1d70346ca11d7f09d/raw/7f7eeea482f5047e62944e54182aa26c89cc299a/whatsapp_python.svg" alt="logo" width="128px">
  <h1>whatsapp-python</h1>
  <p>Free, open-source Python wrapper for the <a href="https://developers.facebook.com/docs/whatsapp/cloud-api">WhatsApp Cloud API</a>.<br>Forked from <a href="https://github.com/Neurotech-HQ/heyoo">Neurotech-HQ/heyoo</a>.
  </p>
  <img src="https://img.shields.io/badge/made%20in-italy-008751.svg?style=flat-square" alt="Made in Italy">
  <a href="https://pepy.tech/project/whatsapp-python"><img src="https://static.pepy.tech/personalized-badge/whatsapp-python?period=total&units=none&left_color=grey&right_color=blue&left_text=Downloads" alt="Downloads"></a>
  <a href="https://pepy.tech/project/whatsapp-python"><img src="https://pepy.tech/badge/whatsapp-python/month" alt="Monthly Downloads"></a>
  <a href="https://pepy.tech/project/whatsapp-python"><img src="https://pepy.tech/badge/whatsapp-python/week" alt="Weekly Downloads"></a>
</div>

## Installation

To install the library you can either use pip (latest release version):

``pip install whatsapp-python``

You can also install the development GitHub version (always up to date, with the latest features and bug fixes):

```bash
git clone https://github.com/filipporomani/whatsapp.git
cd whatsapp
pip install .
```

If you want to use a local enviroment you can also use hatch:
  
```bash
git clone https://github.com/filipporomani/whatsapp.git
cd whatsapp
pip install hatch
hatch shell
```

Documentation is available in the [wiki](https://github.com/filipporomani/whatsapp/wiki) here on GitHub.

### Key features:
- Modern interface using `async` and `await`
- Full support for Graph API error handling
- Optimized for high-load workflows using asyncronous programming
- Always up to date
- All of the WhatsApp Business chat UI features supported


### All features
- Event listening (incoming messages)
- Asyncronous API calls
- Error handling
- Sending messages
- Sending messages from different numbers individually
- Marking messages as read
- Replying to messages
- Reacting to messages
- Sending medias (images, audios, videos, links and documents)
- Sending location
- Sending interactive buttons
- Sending template messages
- Parsing messages and media received
- Sending contacts
- Uploading media to the Facebook cloud

## Obtaining the WhatsApp API credentials

To use the WhatsApp API you need to create a Facebook Business account and a WhatsApp Business account.

> [!TIP]  
> To create an account, I recommend to follow [this video](https://youtu.be/d6lNxP2gadA).

## Pricing of the API

Whereas using third-party providers of the WhatsApp API can result in monthly fees, using the WhatsApp API[^1] offered directly by Facebook is much cheaper, even if the billing documentation is quite difficult to understand.

> [!CAUTION]  
> It is now mandatory to add a credit card to the WhatsApp account (at least for me) in order to use the service. I was even charged a tiny fee for using a non-test phone number (~€1.20), so be careful when using the API! I'm not responsible for any costs you may face by using the API.
>
> The API should be, however, free for testing purposes with the provided test phone number.

All the prices are available in the [**WhatsApp API docs**](https://developers.facebook.com/docs/whatsapp/pricing).

> [!TIP]  
> I recomend to use a test number, as you you can get a free one and use it for testing purposes.

## Migrating from `Neurotech-HQ/heyoo`

*You can ignore this if it's your first time using the library.*

- Any version >1.1.2 is incompatible with the original `heyoo` library! Be careful updating! Read the docs first!
- Any version <=1.1.2 is fully compatible with the original `heyoo` library and doesn't include breaking changes.

Switching from heyoo to whatsapp-python doesn't require any change for versions up to 1.1.2: just uninstall `heyoo`, install `whatsapp-python==1.1.2` and change the import name from `heyoo` to `whatsapp`.

For versions GREATER THAN 1.1.2, messages have became objects, so you need to change your code to use the new methods.

> [!NOTE]  
> Documentation for version 1.1.2 can be found [here](https://github.com/filipporomani/whatsapp/wiki/v1.1.2).

## Contributing

If you are facing any issues or have any questions, please [open a new issue](https://github.com/filipporomani/whatsapp/issues/new/choose)!

*This is an open source project published under the [GNU Affero General Public License v3](LICENSE).*

[^1]: https://developers.facebook.com/docs/whatsapp/cloud-api

```



## File: pyproject.toml
```
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name="whatsapp-python"
authors = [{name="Filippo Romani", email="<EMAIL>"}]
description="Open source Python wrapper for the WhatsApp Cloud API"
readme = "README.md"
requires-python = ">=3.10"
classifiers=[
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Customer Service",
    "Intended Audience :: Education",
    "Intended Audience :: Telecommunications Industry",
    "Topic :: Software Development :: Build Tools",
    "License :: OSI Approved :: GNU Affero General Public License v3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: 3.14",
    "Topic :: Communications :: Chat",
    "Topic :: Communications :: Telephony",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Application",
    "Topic :: Software Development :: Libraries :: Python Modules",

]
dynamic = [
  "version"
]
dependencies = ["fastapi", "uvicorn", "requests_toolbelt", "asyncio", "aiohttp", "python-dotenv", "BeautifulSoup4"]

[project.urls]
Homepage = "https://github.com/filipporomani/whatsapp-python"
Docs = "https://github.com/filipporomani/whatsapp-python/wiki"
"Issues Reporting" = "https://github.com/filipporomani/whatsapp-python/issues"
Download = "https://github.com/filipporomani/whatsapp-python/releases/latest"
Changelog = "https://github.com/filipporomani/whatsapp-python/releases"


[tool.hatch.version]
path = "whatsapp/constants.py"
variable = "VERSION"

[tool.hatch.build.targets.wheel]
packages = ["whatsapp"]
```



## File: LICENSE
```
                    GNU AFFERO GENERAL PUBLIC LICENSE
                       Version 3, 19 November 2007

 Copyright © Filippo Romani
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

                            Preamble

  The GNU Affero General Public License is a free, copyleft license for
software and other kinds of works, specifically designed to ensure
cooperation with the community in the case of network server software.

  The licenses for most software and other practical works are designed
to take away your freedom to share and change the works.  By contrast,
our General Public Licenses are intended to guarantee your freedom to
share and change all versions of a program--to make sure it remains free
software for all its users.

  When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
them if you wish), that you receive source code or can get it if you
want it, that you can change the software or use pieces of it in new
free programs, and that you know you can do these things.

  Developers that use our General Public Licenses protect your rights
with two steps: (1) assert copyright on the software, and (2) offer
you this License which gives you legal permission to copy, distribute
and/or modify the software.

  A secondary benefit of defending all users' freedom is that
improvements made in alternate versions of the program, if they
receive widespread use, become available for other developers to
incorporate.  Many developers of free software are heartened and
encouraged by the resulting cooperation.  However, in the case of
software used on network servers, this result may fail to come about.
The GNU General Public License permits making a modified version and
letting the public access it on a server without ever releasing its
source code to the public.

  The GNU Affero General Public License is designed specifically to
ensure that, in such cases, the modified source code becomes available
to the community.  It requires the operator of a network server to
provide the source code of the modified version running there to the
users of that server.  Therefore, public use of a modified version, on
a publicly accessible server, gives the public access to the source
code of the modified version.

  An older license, called the Affero General Public License and
published by Affero, was designed to accomplish similar goals.  This is
a different license, not a version of the Affero GPL, but Affero has
released a new version of the Affero GPL which permits relicensing under
this license.

  The precise terms and conditions for copying, distribution and
modification follow.

                       TERMS AND CONDITIONS

  0. Definitions.

  "This License" refers to version 3 of the GNU Affero General Public License.

  "Copyright" also means copyright-like laws that apply to other kinds of
works, such as semiconductor masks.

  "The Program" refers to any copyrightable work licensed under this
License.  Each licensee is addressed as "you".  "Licensees" and
"recipients" may be individuals or organizations.

  To "modify" a work means to copy from or adapt all or part of the work
in a fashion requiring copyright permission, other than the making of an
exact copy.  The resulting work is called a "modified version" of the
earlier work or a work "based on" the earlier work.

  A "covered work" means either the unmodified Program or a work based
on the Program.

  To "propagate" a work means to do anything with it that, without
permission, would make you directly or secondarily liable for
infringement under applicable copyright law, except executing it on a
computer or modifying a private copy.  Propagation includes copying,
distribution (with or without modification), making available to the
public, and in some countries other activities as well.

  To "convey" a work means any kind of propagation that enables other
parties to make or receive copies.  Mere interaction with a user through
a computer network, with no transfer of a copy, is not conveying.

  An interactive user interface displays "Appropriate Legal Notices"
to the extent that it includes a convenient and prominently visible
feature that (1) displays an appropriate copyright notice, and (2)
tells the user that there is no warranty for the work (except to the
extent that warranties are provided), that licensees may convey the
work under this License, and how to view a copy of this License.  If
the interface presents a list of user commands or options, such as a
menu, a prominent item in the list meets this criterion.

  1. Source Code.

  The "source code" for a work means the preferred form of the work
for making modifications to it.  "Object code" means any non-source
form of a work.

  A "Standard Interface" means an interface that either is an official
standard defined by a recognized standards body, or, in the case of
interfaces specified for a particular programming language, one that
is widely used among developers working in that language.

  The "System Libraries" of an executable work include anything, other
than the work as a whole, that (a) is included in the normal form of
packaging a Major Component, but which is not part of that Major
Component, and (b) serves only to enable use of the work with that
Major Component, or to implement a Standard Interface for which an
implementation is available to the public in source code form.  A
"Major Component", in this context, means a major essential component
(kernel, window system, and so on) of the specific operating system
(if any) on which the executable work runs, or a compiler used to
produce the work, or an object code interpreter used to run it.

  The "Corresponding Source" for a work in object code form means all
the source code needed to generate, install, and (for an executable
work) run the object code and to modify the work, including scripts to
control those activities.  However, it does not include the work's
System Libraries, or general-purpose tools or generally available free
programs which are used unmodified in performing those activities but
which are not part of the work.  For example, Corresponding Source
includes interface definition files associated with source files for
the work, and the source code for shared libraries and dynamically
linked subprograms that the work is specifically designed to require,
such as by intimate data communication or control flow between those
subprograms and other parts of the work.

  The Corresponding Source need not include anything that users
can regenerate automatically from other parts of the Corresponding
Source.

  The Corresponding Source for a work in source code form is that
same work.

  2. Basic Permissions.

  All rights granted under this License are granted for the term of
copyright on the Program, and are irrevocable provided the stated
conditions are met.  This License explicitly affirms your unlimited
permission to run the unmodified Program.  The output from running a
covered work is covered by this License only if the output, given its
content, constitutes a covered work.  This License acknowledges your
rights of fair use or other equivalent, as provided by copyright law.

  You may make, run and propagate covered works that you do not
convey, without conditions so long as your license otherwise remains
in force.  You may convey covered works to others for the sole purpose
of having them make modifications exclusively for you, or provide you
with facilities for running those works, provided that you comply with
the terms of this License in conveying all material for which you do
not control copyright.  Those thus making or running the covered works
for you must do so exclusively on your behalf, under your direction
and control, on terms that prohibit them from making any copies of
your copyrighted material outside their relationship with you.

  Conveying under any other circumstances is permitted solely under
the conditions stated below.  Sublicensing is not allowed; section 10
makes it unnecessary.

  3. Protecting Users' Legal Rights From Anti-Circumvention Law.

  No covered work shall be deemed part of an effective technological
measure under any applicable law fulfilling obligations under article
11 of the WIPO copyright treaty adopted on 20 December 1996, or
similar laws prohibiting or restricting circumvention of such
measures.

  When you convey a covered work, you waive any legal power to forbid
circumvention of technological measures to the extent such circumvention
is effected by exercising rights under this License with respect to
the covered work, and you disclaim any intention to limit operation or
modification of the work as a means of enforcing, against the work's
users, your or third parties' legal rights to forbid circumvention of
technological measures.

  4. Conveying Verbatim Copies.

  You may convey verbatim copies of the Program's source code as you
receive it, in any medium, provided that you conspicuously and
appropriately publish on each copy an appropriate copyright notice;
keep intact all notices stating that this License and any
non-permissive terms added in accord with section 7 apply to the code;
keep intact all notices of the absence of any warranty; and give all
recipients a copy of this License along with the Program.

  You may charge any price or no price for each copy that you convey,
and you may offer support or warranty protection for a fee.

  5. Conveying Modified Source Versions.

  You may convey a work based on the Program, or the modifications to
produce it from the Program, in the form of source code under the
terms of section 4, provided that you also meet all of these conditions:

    a) The work must carry prominent notices stating that you modified
    it, and giving a relevant date.

    b) The work must carry prominent notices stating that it is
    released under this License and any conditions added under section
    7.  This requirement modifies the requirement in section 4 to
    "keep intact all notices".

    c) You must license the entire work, as a whole, under this
    License to anyone who comes into possession of a copy.  This
    License will therefore apply, along with any applicable section 7
    additional terms, to the whole of the work, and all its parts,
    regardless of how they are packaged.  This License gives no
    permission to license the work in any other way, but it does not
    invalidate such permission if you have separately received it.

    d) If the work has interactive user interfaces, each must display
    Appropriate Legal Notices; however, if the Program has interactive
    interfaces that do not display Appropriate Legal Notices, your
    work need not make them do so.

  A compilation of a covered work with other separate and independent
works, which are not by their nature extensions of the covered work,
and which are not combined with it such as to form a larger program,
in or on a volume of a storage or distribution medium, is called an
"aggregate" if the compilation and its resulting copyright are not
used to limit the access or legal rights of the compilation's users
beyond what the individual works permit.  Inclusion of a covered work
in an aggregate does not cause this License to apply to the other
parts of the aggregate.

  6. Conveying Non-Source Forms.

  You may convey a covered work in object code form under the terms
of sections 4 and 5, provided that you also convey the
machine-readable Corresponding Source under the terms of this License,
in one of these ways:

    a) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by the
    Corresponding Source fixed on a durable physical medium
    customarily used for software interchange.

    b) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by a
    written offer, valid for at least three years and valid for as
    long as you offer spare parts or customer support for that product
    model, to give anyone who possesses the object code either (1) a
    copy of the Corresponding Source for all the software in the
    product that is covered by this License, on a durable physical
    medium customarily used for software interchange, for a price no
    more than your reasonable cost of physically performing this
    conveying of source, or (2) access to copy the
    Corresponding Source from a network server at no charge.

    c) Convey individual copies of the object code with a copy of the
    written offer to provide the Corresponding Source.  This
    alternative is allowed only occasionally and noncommercially, and
    only if you received the object code with such an offer, in accord
    with subsection 6b.

    d) Convey the object code by offering access from a designated
    place (gratis or for a charge), and offer equivalent access to the
    Corresponding Source in the same way through the same place at no
    further charge.  You need not require recipients to copy the
    Corresponding Source along with the object code.  If the place to
    copy the object code is a network server, the Corresponding Source
    may be on a different server (operated by you or a third party)
    that supports equivalent copying facilities, provided you maintain
    clear directions next to the object code saying where to find the
    Corresponding Source.  Regardless of what server hosts the
    Corresponding Source, you remain obligated to ensure that it is
    available for as long as needed to satisfy these requirements.

    e) Convey the object code using peer-to-peer transmission, provided
    you inform other peers where the object code and Corresponding
    Source of the work are being offered to the general public at no
    charge under subsection 6d.

  A separable portion of the object code, whose source code is excluded
from the Corresponding Source as a System Library, need not be
included in conveying the object code work.

  A "User Product" is either (1) a "consumer product", which means any
tangible personal property which is normally used for personal, family,
or household purposes, or (2) anything designed or sold for incorporation
into a dwelling.  In determining whether a product is a consumer product,
doubtful cases shall be resolved in favor of coverage.  For a particular
product received by a particular user, "normally used" refers to a
typical or common use of that class of product, regardless of the status
of the particular user or of the way in which the particular user
actually uses, or expects or is expected to use, the product.  A product
is a consumer product regardless of whether the product has substantial
commercial, industrial or non-consumer uses, unless such uses represent
the only significant mode of use of the product.

  "Installation Information" for a User Product means any methods,
procedures, authorization keys, or other information required to install
and execute modified versions of a covered work in that User Product from
a modified version of its Corresponding Source.  The information must
suffice to ensure that the continued functioning of the modified object
code is in no case prevented or interfered with solely because
modification has been made.

  If you convey an object code work under this section in, or with, or
specifically for use in, a User Product, and the conveying occurs as
part of a transaction in which the right of possession and use of the
User Product is transferred to the recipient in perpetuity or for a
fixed term (regardless of how the transaction is characterized), the
Corresponding Source conveyed under this section must be accompanied
by the Installation Information.  But this requirement does not apply
if neither you nor any third party retains the ability to install
modified object code on the User Product (for example, the work has
been installed in ROM).

  The requirement to provide Installation Information does not include a
requirement to continue to provide support service, warranty, or updates
for a work that has been modified or installed by the recipient, or for
the User Product in which it has been modified or installed.  Access to a
network may be denied when the modification itself materially and
adversely affects the operation of the network or violates the rules and
protocols for communication across the network.

  Corresponding Source conveyed, and Installation Information provided,
in accord with this section must be in a format that is publicly
documented (and with an implementation available to the public in
source code form), and must require no special password or key for
unpacking, reading or copying.

  7. Additional Terms.

  "Additional permissions" are terms that supplement the terms of this
License by making exceptions from one or more of its conditions.
Additional permissions that are applicable to the entire Program shall
be treated as though they were included in this License, to the extent
that they are valid under applicable law.  If additional permissions
apply only to part of the Program, that part may be used separately
under those permissions, but the entire Program remains governed by
this License without regard to the additional permissions.

  When you convey a copy of a covered work, you may at your option
remove any additional permissions from that copy, or from any part of
it.  (Additional permissions may be written to require their own
removal in certain cases when you modify the work.)  You may place
additional permissions on material, added by you to a covered work,
for which you have or can give appropriate copyright permission.

  Notwithstanding any other provision of this License, for material you
add to a covered work, you may (if authorized by the copyright holders of
that material) supplement the terms of this License with terms:

    a) Disclaiming warranty or limiting liability differently from the
    terms of sections 15 and 16 of this License; or

    b) Requiring preservation of specified reasonable legal notices or
    author attributions in that material or in the Appropriate Legal
    Notices displayed by works containing it; or

    c) Prohibiting misrepresentation of the origin of that material, or
    requiring that modified versions of such material be marked in
    reasonable ways as different from the original version; or

    d) Limiting the use for publicity purposes of names of licensors or
    authors of the material; or

    e) Declining to grant rights under trademark law for use of some
    trade names, trademarks, or service marks; or

    f) Requiring indemnification of licensors and authors of that
    material by anyone who conveys the material (or modified versions of
    it) with contractual assumptions of liability to the recipient, for
    any liability that these contractual assumptions directly impose on
    those licensors and authors.

  All other non-permissive additional terms are considered "further
restrictions" within the meaning of section 10.  If the Program as you
received it, or any part of it, contains a notice stating that it is
governed by this License along with a term that is a further
restriction, you may remove that term.  If a license document contains
a further restriction but permits relicensing or conveying under this
License, you may add to a covered work material governed by the terms
of that license document, provided that the further restriction does
not survive such relicensing or conveying.

  If you add terms to a covered work in accord with this section, you
must place, in the relevant source files, a statement of the
additional terms that apply to those files, or a notice indicating
where to find the applicable terms.

  Additional terms, permissive or non-permissive, may be stated in the
form of a separately written license, or stated as exceptions;
the above requirements apply either way.

  8. Termination.

  You may not propagate or modify a covered work except as expressly
provided under this License.  Any attempt otherwise to propagate or
modify it is void, and will automatically terminate your rights under
this License (including any patent licenses granted under the third
paragraph of section 11).

  However, if you cease all violation of this License, then your
license from a particular copyright holder is reinstated (a)
provisionally, unless and until the copyright holder explicitly and
finally terminates your license, and (b) permanently, if the copyright
holder fails to notify you of the violation by some reasonable means
prior to 60 days after the cessation.

  Moreover, your license from a particular copyright holder is
reinstated permanently if the copyright holder notifies you of the
violation by some reasonable means, this is the first time you have
received notice of violation of this License (for any work) from that
copyright holder, and you cure the violation prior to 30 days after
your receipt of the notice.

  Termination of your rights under this section does not terminate the
licenses of parties who have received copies or rights from you under
this License.  If your rights have been terminated and not permanently
reinstated, you do not qualify to receive new licenses for the same
material under section 10.

  9. Acceptance Not Required for Having Copies.

  You are not required to accept this License in order to receive or
run a copy of the Program.  Ancillary propagation of a covered work
occurring solely as a consequence of using peer-to-peer transmission
to receive a copy likewise does not require acceptance.  However,
nothing other than this License grants you permission to propagate or
modify any covered work.  These actions infringe copyright if you do
not accept this License.  Therefore, by modifying or propagating a
covered work, you indicate your acceptance of this License to do so.

  10. Automatic Licensing of Downstream Recipients.

  Each time you convey a covered work, the recipient automatically
receives a license from the original licensors, to run, modify and
propagate that work, subject to this License.  You are not responsible
for enforcing compliance by third parties with this License.

  An "entity transaction" is a transaction transferring control of an
organization, or substantially all assets of one, or subdividing an
organization, or merging organizations.  If propagation of a covered
work results from an entity transaction, each party to that
transaction who receives a copy of the work also receives whatever
licenses to the work the party's predecessor in interest had or could
give under the previous paragraph, plus a right to possession of the
Corresponding Source of the work from the predecessor in interest, if
the predecessor has it or can get it with reasonable efforts.

  You may not impose any further restrictions on the exercise of the
rights granted or affirmed under this License.  For example, you may
not impose a license fee, royalty, or other charge for exercise of
rights granted under this License, and you may not initiate litigation
(including a cross-claim or counterclaim in a lawsuit) alleging that
any patent claim is infringed by making, using, selling, offering for
sale, or importing the Program or any portion of it.

  11. Patents.

  A "contributor" is a copyright holder who authorizes use under this
License of the Program or a work on which the Program is based.  The
work thus licensed is called the contributor's "contributor version".

  A contributor's "essential patent claims" are all patent claims
owned or controlled by the contributor, whether already acquired or
hereafter acquired, that would be infringed by some manner, permitted
by this License, of making, using, or selling its contributor version,
but do not include claims that would be infringed only as a
consequence of further modification of the contributor version.  For
purposes of this definition, "control" includes the right to grant
patent sublicenses in a manner consistent with the requirements of
this License.

  Each contributor grants you a non-exclusive, worldwide, royalty-free
patent license under the contributor's essential patent claims, to
make, use, sell, offer for sale, import and otherwise run, modify and
propagate the contents of its contributor version.

  In the following three paragraphs, a "patent license" is any express
agreement or commitment, however denominated, not to enforce a patent
(such as an express permission to practice a patent or covenant not to
sue for patent infringement).  To "grant" such a patent license to a
party means to make such an agreement or commitment not to enforce a
patent against the party.

  If you convey a covered work, knowingly relying on a patent license,
and the Corresponding Source of the work is not available for anyone
to copy, free of charge and under the terms of this License, through a
publicly available network server or other readily accessible means,
then you must either (1) cause the Corresponding Source to be so
available, or (2) arrange to deprive yourself of the benefit of the
patent license for this particular work, or (3) arrange, in a manner
consistent with the requirements of this License, to extend the patent
license to downstream recipients.  "Knowingly relying" means you have
actual knowledge that, but for the patent license, your conveying the
covered work in a country, or your recipient's use of the covered work
in a country, would infringe one or more identifiable patents in that
country that you have reason to believe are valid.

  If, pursuant to or in connection with a single transaction or
arrangement, you convey, or propagate by procuring conveyance of, a
covered work, and grant a patent license to some of the parties
receiving the covered work authorizing them to use, propagate, modify
or convey a specific copy of the covered work, then the patent license
you grant is automatically extended to all recipients of the covered
work and works based on it.

  A patent license is "discriminatory" if it does not include within
the scope of its coverage, prohibits the exercise of, or is
conditioned on the non-exercise of one or more of the rights that are
specifically granted under this License.  You may not convey a covered
work if you are a party to an arrangement with a third party that is
in the business of distributing software, under which you make payment
to the third party based on the extent of your activity of conveying
the work, and under which the third party grants, to any of the
parties who would receive the covered work from you, a discriminatory
patent license (a) in connection with copies of the covered work
conveyed by you (or copies made from those copies), or (b) primarily
for and in connection with specific products or compilations that
contain the covered work, unless you entered into that arrangement,
or that patent license was granted, prior to 28 March 2007.

  Nothing in this License shall be construed as excluding or limiting
any implied license or other defenses to infringement that may
otherwise be available to you under applicable patent law.

  12. No Surrender of Others' Freedom.

  If conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot convey a
covered work so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you may
not convey it at all.  For example, if you agree to terms that obligate you
to collect a royalty for further conveying from those to whom you convey
the Program, the only way you could satisfy both those terms and this
License would be to refrain entirely from conveying the Program.

  13. Remote Network Interaction; Use with the GNU General Public License.

  Notwithstanding any other provision of this License, if you modify the
Program, your modified version must prominently offer all users
interacting with it remotely through a computer network (if your version
supports such interaction) an opportunity to receive the Corresponding
Source of your version by providing access to the Corresponding Source
from a network server at no charge, through some standard or customary
means of facilitating copying of software.  This Corresponding Source
shall include the Corresponding Source for any work covered by version 3
of the GNU General Public License that is incorporated pursuant to the
following paragraph.

  Notwithstanding any other provision of this License, you have
permission to link or combine any covered work with a work licensed
under version 3 of the GNU General Public License into a single
combined work, and to convey the resulting work.  The terms of this
License will continue to apply to the part which is the covered work,
but the work with which it is combined will remain governed by version
3 of the GNU General Public License.

  14. Revised Versions of this License.

  The Free Software Foundation may publish revised and/or new versions of
the GNU Affero General Public License from time to time.  Such new versions
will be similar in spirit to the present version, but may differ in detail to
address new problems or concerns.

  Each version is given a distinguishing version number.  If the
Program specifies that a certain numbered version of the GNU Affero General
Public License "or any later version" applies to it, you have the
option of following the terms and conditions either of that numbered
version or of any later version published by the Free Software
Foundation.  If the Program does not specify a version number of the
GNU Affero General Public License, you may choose any version ever published
by the Free Software Foundation.

  If the Program specifies that a proxy can decide which future
versions of the GNU Affero General Public License can be used, that proxy's
public statement of acceptance of a version permanently authorizes you
to choose that version for the Program.

  Later license versions may give you additional or different
permissions.  However, no additional obligations are imposed on any
author or copyright holder as a result of your choosing to follow a
later version.

  15. Disclaimer of Warranty.

  THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY
APPLICABLE LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT
HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY
OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM
IS WITH YOU.  SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF
ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

  16. Limitation of Liability.

  IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MODIFIES AND/OR CONVEYS
THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY
GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE
USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF
DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD
PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS),
EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF
SUCH DAMAGES.

  17. Interpretation of Sections 15 and 16.

  If the disclaimer of warranty and limitation of liability provided
above cannot be given local legal effect according to their terms,
reviewing courts shall apply local law that most closely approximates
an absolute waiver of all civil liability in connection with the
Program, unless a warranty or assumption of liability accompanies a
copy of the Program in return for a fee.

                     END OF TERMS AND CONDITIONS

            How to Apply These Terms to Your New Programs

  If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

  To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
state the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

    <one line to give the program's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU Affero General Public License as published
    by the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU Affero General Public License for more details.

    You should have received a copy of the GNU Affero General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

Also add information on how to contact you by electronic and paper mail.

  If your software can interact with users remotely through a computer
network, you should also make sure that it provides a way for users to
get its source.  For example, if your program is a web application, its
interface could display a "Source" link that leads users to an archive
of the code.  There are many ways you could offer source, and different
solutions will be better for different programs; see section 13 for the
specific requirements.

  You should also get your employer (if you work as a programmer) or school,
if any, to sign a "copyright disclaimer" for the program, if necessary.
For more information on this, and how to apply and follow the GNU AGPL, see
<https://www.gnu.org/licenses/>.

```



## File: CODE_OF_CONDUCT.md
```
# Contributor Covenant Code of Conduct

## Our Pledge

We as members, contributors, and leaders pledge to make participation in our
community a harassment-free experience for everyone, regardless of age, body
size, visible or invisible disability, ethnicity, sex characteristics, gender
identity and expression, level of experience, education, socio-economic status,
nationality, personal appearance, race, religion, or sexual identity
and orientation.

We pledge to act and interact in ways that contribute to an open, welcoming,
diverse, inclusive, and healthy community.

## Our Standards

Examples of behavior that contributes to a positive environment for our
community include:

* Demonstrating empathy and kindness toward other people
* Being respectful of differing opinions, viewpoints, and experiences
* Giving and gracefully accepting constructive feedback
* Accepting responsibility and apologizing to those affected by our mistakes,
  and learning from the experience
* Focusing on what is best not just for us as individuals, but for the
  overall community

Examples of unacceptable behavior include:

* The use of sexualized language or imagery, and sexual attention or
  advances of any kind
* Trolling, insulting or derogatory comments, and personal or political attacks
* Public or private harassment
* Publishing others' private information, such as a physical or email
  address, without their explicit permission
* Other conduct which could reasonably be considered inappropriate in a
  professional setting

## Enforcement Responsibilities

Community leaders are responsible for clarifying and enforcing our standards of
acceptable behavior and will take appropriate and fair corrective action in
response to any behavior that they deem inappropriate, threatening, offensive,
or harmful.

Community leaders have the right and responsibility to remove, edit, or reject
comments, commits, code, wiki edits, issues, and other contributions that are
not aligned to this Code of Conduct, and will communicate reasons for moderation
decisions when appropriate.

## Scope

This Code of Conduct applies within all community spaces, and also applies when
an individual is officially representing the community in public spaces.
Examples of representing our community include using an official e-mail address,
posting via an official social media account, or acting as an appointed
representative at an online or offline event.

## Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be
reported to the community leaders responsible for enforcement at
<EMAIL>.
All complaints will be reviewed and investigated promptly and fairly.

All community leaders are obligated to respect the privacy and security of the
reporter of any incident.

## Enforcement Guidelines

Community leaders will follow these Community Impact Guidelines in determining
the consequences for any action they deem in violation of this Code of Conduct:

### 1. Correction

**Community Impact**: Use of inappropriate language or other behavior deemed
unprofessional or unwelcome in the community.

**Consequence**: A private, written warning from community leaders, providing
clarity around the nature of the violation and an explanation of why the
behavior was inappropriate. A public apology may be requested.

### 2. Warning

**Community Impact**: A violation through a single incident or series
of actions.

**Consequence**: A warning with consequences for continued behavior. No
interaction with the people involved, including unsolicited interaction with
those enforcing the Code of Conduct, for a specified period of time. This
includes avoiding interactions in community spaces as well as external channels
like social media. Violating these terms may lead to a temporary or
permanent ban.

### 3. Temporary Ban

**Community Impact**: A serious violation of community standards, including
sustained inappropriate behavior.

**Consequence**: A temporary ban from any sort of interaction or public
communication with the community for a specified period of time. No public or
private interaction with the people involved, including unsolicited interaction
with those enforcing the Code of Conduct, is allowed during this period.
Violating these terms may lead to a permanent ban.

### 4. Permanent Ban

**Community Impact**: Demonstrating a pattern of violation of community
standards, including sustained inappropriate behavior,  harassment of an
individual, or aggression toward or disparagement of classes of individuals.

**Consequence**: A permanent ban from any sort of public interaction within
the community.

## Attribution

This Code of Conduct is adapted from the [Contributor Covenant][homepage],
version 2.0, available at
https://www.contributor-covenant.org/version/2/0/code_of_conduct.html.

Community Impact Guidelines were inspired by [Mozilla's code of conduct
enforcement ladder](https://github.com/mozilla/diversity).

[homepage]: https://www.contributor-covenant.org

For answers to common questions about this code of conduct, see the FAQ at
https://www.contributor-covenant.org/faq. Translations are available at
https://www.contributor-covenant.org/translations.

```



## File: CONTRIBUTING.md
```
# How to contribute

First of all, thank you for considering contributing to this project! It's people like you that make it a reality and help me keep it alive and updated.

If you are unsure about anything, feel free to ask me. I am here to help you.

Any bug report, feature request, or code contribution is welcome. Here are some guidelines to help you get started.

## Submitting changes

Please open a [Pull Request](https://github.com/filipporomani/whatsapp-python/pull/new/master) with a list of what you've done (read more about [pull requests](http://help.github.com/pull-requests/)). Make sure, if possible, that all of your commits are atomic (one feature per commit).

Always write a clear log message for your commits. One-line messages are fine for small changes, but bigger changes should look like this:

    $ git commit -m "feat: a brief summary of the commit
    > 
    > A paragraph describing what changed and its impact."


  * This is an open source software. Keep in mind to make your code readable to others. It's sort of like driving a car: perhaps you love doing donuts when you're alone, but with passengers the goal is to make the ride as smooth as possible.

There isn't any other particular guideline, feel free to contribute!

```



## File: SECURITY.md
```
# Security Policy

## Supported Versions

| Version | Supported          |
| ------- | ------------------ |
| >=3.3.96 | :white_check_mark: |
| <3.3.96  | :x:                |

## Reporting a Vulnerability

Please report any vulnerability via e-mail: <EMAIL>.
I'll review vulnerabilities as soon as possible, with maximum prioirity. 
If you find any security issue that is not crucial, feel free to open an issue for it.

```



## File: .gitignore
```
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
test.py

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.DS_Store

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
whatsapp/.DS_Store

```



## Source: whatsapp/__init__.py
```
"""
Unofficial Python wrapper for the WhatsApp Cloud API.
"""

from __future__ import annotations
import requests
import logging
import json
import asyncio
import aiohttp
from bs4 import BeautifulSoup
from fastapi import FastAPI, HTTPException, Request
from uvicorn import run as _run
from .constants import VERSION
from .ext._property import authorized
from .ext._send_others import send_custom_json, send_contacts
from .ext._message import send_template
from .ext._send_media import (
    send_image,
    send_video,
    send_audio,
    send_location,
    send_sticker,
    send_document,
)
from .ext._media import upload_media, query_media_url, download_media, delete_media
from .ext._buttons import send_button, create_button, send_reply_button
from .ext._static import (
    is_message,
    get_mobile,
    get_author,
    get_name,
    get_message,
    get_message_id,
    get_message_type,
    get_message_timestamp,
    get_audio,
    get_delivery,
    get_document,
    get_image,
    get_sticker,
    get_interactive_response,
    get_location,
    get_video,
    changed_field,
)

from .async_ext._property import authorized as async_authorized
from .async_ext._send_others import (
    send_custom_json as async_send_custom_json,
    send_contacts as async_send_contacts,
)
from .async_ext._message import send_template as async_send_template
from .async_ext._send_media import (
    send_image as async_send_image,
    send_video as async_send_video,
    send_audio as async_send_audio,
    send_location as async_send_location,
    send_sticker as async_send_sticker,
    send_document as async_send_document,
)
from .async_ext._media import (
    upload_media as async_upload_media,
    query_media_url as async_query_media_url,
    download_media as async_download_media,
    delete_media as async_delete_media,
)
from .async_ext._buttons import (
    send_button as async_send_button,
    create_button as async_create_button,
    send_reply_button as async_send_reply_button,
)
from .async_ext._static import (
    is_message as async_is_message,
    get_mobile as async_get_mobile,
    get_author as async_get_author,
    get_name as async_get_name,
    get_message as async_get_message,
    get_message_id as async_get_message_id,
    get_message_type as async_get_message_type,
    get_message_timestamp as async_get_message_timestamp,
    get_audio as async_get_audio,
    get_delivery as async_get_delivery,
    get_document as async_get_document,
    get_image as async_get_image,
    get_sticker as async_get_sticker,
    get_interactive_response as async_get_interactive_response,
    get_location as async_get_location,
    get_video as async_get_video,
    changed_field as async_changed_field,
)

from .errors import Handle


class WhatsApp:
    def __init__(
        self,
        token: str = "",
        phone_number_id: dict = "",
        logger: bool = True,
        update_check: bool = True,
        verify_token: str = "",
        debug: bool = True,
        version: str = "latest",
    ):
        """
        Initialize the WhatsApp Object

        Args:
            token[str]: Token for the WhatsApp cloud API obtained from the Facebook developer portal
            phone_number_id[str]: Phone number id for the WhatsApp cloud API obtained from the developer portal
            logger[bool]: Whether to enable logging or not (default: True)
        """

        # Check if the version is up to date
        logging.getLogger(__name__).addHandler(logging.NullHandler())

        self.l = phone_number_id

        if isinstance(phone_number_id, dict):
            # use first phone number id as default
            phone_number_id = phone_number_id[list(phone_number_id.keys())[0]]

        elif phone_number_id == "":
            logging.error("Phone number ID not provided")
            raise ValueError("Phone number ID not provided but required")
        elif isinstance(phone_number_id, str):
            logging.critical(
                "The phone number ID should be a dictionary of phone numbers and their IDs. Using strings is deprecated."
            )
            raise ValueError(
                "Phone number ID should be a dictionary of phone numbers and their IDs"
            )
        else:
            pass

        self.VERSION = VERSION  # package version
        r = requests.get(
            "https://developers.facebook.com/docs/graph-api/changelog/"
        ).text

        # dynamically get the latest version of the API
        if version == "latest":
            soup = BeautifulSoup(r, features="html.parser")
            t1 = soup.findAll("table")

            def makeversion(table: BeautifulSoup) -> str:
                result = []
                allrows = table.findAll("tr")
                for row in allrows:
                    result.append([])
                    allcols = row.findAll("td")
                    for col in allcols:
                        thestrings = [(s) for s in col.findAll(text=True)]
                        thetext = "".join(thestrings)
                        result[-1].append(thetext)
                return result[0][1]

            self.LATEST = makeversion(t1[0])  # latest version of the API
        else:
            self.LATEST = version

        if update_check is True:
            latest = str(
                requests.get("https://pypi.org/pypi/whatsapp-python/json").json()[
                    "info"
                ]["version"]
            )
            if self.VERSION != latest:
                try:
                    version_int = int(self.VERSION.replace(".", ""))
                except:
                    version_int = 0
                try:
                    latest_int = int(latest.replace(".", ""))
                except:
                    latest_int = 0
                # this is to avoid the case where the version is 1.0.10 and the latest is 1.0.2 (possible if user is using the github version)
                if version_int < latest_int:
                    if version_int == 0:
                        logging.critical(
                            f"There was an error while checking for updates, please check for updates manually. This may be due to the version being a post-release version (e.g. 1.0.0.post1) or a pre-release version (e.g. 1.0.0a1). READ THE CHANGELOG BEFORE UPDATING. NEW VERSIONS MAY BREAK YOUR CODE IF NOT PROPERLY UPDATED."
                        )
                    else:
                        logging.critical(
                            f"Whatsapp-python is out of date. Please update to the latest version {latest}. READ THE CHANGELOG BEFORE UPDATING. NEW VERSIONS MAY BREAK YOUR CODE IF NOT PROPERLY UPDATED."
                        )

        if token == "":
            logging.error("Token not provided")
            raise ValueError("Token not provided but required")
        if phone_number_id == "":
            logging.error("Phone number ID not provided")
            raise ValueError("Phone number ID not provided but required")
        self.token = token
        self.phone_number_id = phone_number_id
        self.base_url = f"https://graph.facebook.com/{self.LATEST}"
        self.url = f"{self.base_url}/{phone_number_id}/messages"
        self.verify_token = verify_token

        async def base(*args):
            pass

        self.message_handler = base
        self.other_handler = base
        self.verification_handler = base
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }
        if logger is False:
            logging.disable(logging.INFO)
            logging.disable(logging.ERROR)
        if debug is False:
            logging.disable(logging.DEBUG)
            logging.disable(logging.ERROR)

        self.app = FastAPI()

        # Verification handler has 1 argument: challenge (str | bool): str if verification is successful, False if not

        @self.app.get("/")
        async def verify_endpoint(r: Request):
            if r.query_params.get("hub.verify_token") == self.verify_token:
                logging.debug("Webhook verified successfully")
                challenge = r.query_params.get("hub.challenge")
                await self.verification_handler(challenge)
                await self.other_handler(challenge)
                return int(challenge)
            logging.error("Webhook Verification failed - token mismatch")
            await self.verification_handler(False)
            await self.other_handler(False)
            return {"success": False}

        @self.app.post("/")
        async def hook(r: Request):
            try:
                # Handle Webhook Subscriptions
                data = await r.json()
                if data is None:
                    return {"success": False}
                data_str = json.dumps(data, indent=4)
                # log the data received only if the log level is debug
                logging.debug(f"Received webhook data: {data_str}")

                changed_field = self.changed_field(data)
                if changed_field == "messages":
                    new_message = self.is_message(data)
                    if new_message:
                        msg = Message(instance=self, data=data)
                        await self.message_handler(msg)
                        await self.other_handler(msg)
                return {"success": True}
            except Exception as e:
                logging.error(f"Error parsing message: {e}")
                raise HTTPException(
                    status_code=500, detail={"success": False, "error": str(e)}
                )

    # all the files starting with _ are imported here, and should not be imported directly.

    is_message = staticmethod(is_message)
    get_mobile = staticmethod(get_mobile)
    get_name = staticmethod(get_name)
    get_message = staticmethod(get_message)
    get_message_id = staticmethod(get_message_id)
    get_message_type = staticmethod(get_message_type)
    get_message_timestamp = staticmethod(get_message_timestamp)
    get_audio = staticmethod(get_audio)
    get_delivery = staticmethod(get_delivery)
    get_document = staticmethod(get_document)
    get_image = staticmethod(get_image)
    get_sticker = staticmethod(get_sticker)
    get_interactive_response = staticmethod(get_interactive_response)
    get_location = staticmethod(get_location)
    get_video = staticmethod(get_video)
    changed_field = staticmethod(changed_field)
    get_author = staticmethod(get_author)

    send_button = send_button
    create_button = create_button
    send_reply_button = send_reply_button
    send_image = send_image
    send_video = send_video
    send_audio = send_audio
    send_location = send_location
    send_sticker = send_sticker
    send_document = send_document
    upload_media = upload_media
    query_media_url = query_media_url
    download_media = download_media
    delete_media = delete_media
    send_template = send_template
    send_custom_json = send_custom_json
    send_contacts = send_contacts
    authorized = property(authorized)

    def create_message(self, **kwargs) -> Message:
        """
        Create a message object

        Args:
            data[dict]: The message data
            content[str]: The message content
            to[str]: The recipient
            rec_type[str]: The recipient type (individual/group)
        """
        return Message(**kwargs, instance=self)

    def on_message(self, handler: function):
        """
        Set the handler for incoming messages

        Args:
            handler[function]: The handler function
        """
        self.message_handler = handler

    def on_event(self, handler: function):
        """
        Set the handler for other events

        Args:
            handler[function]: The handler function
        """
        self.other_handler = handler

    def on_verification(self, handler: function):
        """
        Set the handler for verification

        Args:
            handler[function]: The handler function
        """
        self.verification_handler = handler

    def run(self, host: str = "localhost", port: int = 5000, **options):
        _run(self.app, host=host, port=port, **options)


class AsyncWhatsApp(WhatsApp):
    def __init__(
        self,
        token: str = "",
        phone_number_id: dict = "",
        logger: bool = True,
        update_check: bool = True,
        verify_token: str = "",
        debug: bool = True,
        version: str = "latest",
    ):
        """
        Initialize the WhatsApp Object

        Args:
            token[str]: Token for the WhatsApp cloud API obtained from the Facebook developer portal
            phone_number_id[str]: Phone number id for the WhatsApp cloud API obtained from the developer portal
            logger[bool]: Whether to enable logging or not (default: True)
        """

        # Check if the version is up to date
        logging.getLogger(__name__).addHandler(logging.NullHandler())
        self.l = phone_number_id

        if isinstance(phone_number_id, dict):
            # use first phone number id as default
            phone_number_id = phone_number_id[list(phone_number_id.keys())[0]]

        elif phone_number_id == "":
            logging.error("Phone number ID not provided")
            raise ValueError("Phone number ID not provided but required")
        elif isinstance(phone_number_id, str):
            logging.critical(
                "The phone number ID should be a dictionary of phone numbers and their IDs. Using strings is deprecated."
            )
            raise ValueError(
                "Phone number ID should be a dictionary of phone numbers and their IDs"
            )
        else:
            pass

        self.VERSION = VERSION  # package version
        r = requests.get(
            "https://developers.facebook.com/docs/graph-api/changelog/"
        ).text

        # dynamically get the latest version of the API
        if version == "latest":
            soup = BeautifulSoup(r, features="html.parser")
            t1 = soup.findAll("table")

            def makeversion(table: BeautifulSoup) -> str:
                result = []
                allrows = table.findAll("tr")
                for row in allrows:
                    result.append([])
                    allcols = row.findAll("td")
                    for col in allcols:
                        thestrings = [(s) for s in col.findAll(text=True)]
                        thetext = "".join(thestrings)
                        result[-1].append(thetext)
                return result[0][1]

            self.LATEST = makeversion(t1[0])  # latest version of the API
        else:
            self.LATEST = version

        if update_check is True:
            latest = str(
                requests.get("https://pypi.org/pypi/whatsapp-python/json").json()[
                    "info"
                ]["version"]
            )
            if self.VERSION != latest:
                try:
                    version_int = int(self.VERSION.replace(".", ""))
                except:
                    version_int = 0
                try:
                    latest_int = int(latest.replace(".", ""))
                except:
                    latest_int = 0
                # this is to avoid the case where the version is 1.0.10 and the latest is 1.0.2 (possible if user is using the github version)
                if version_int < latest_int:
                    if version_int == 0:
                        logging.critical(
                            f"There was an error while checking for updates, please check for updates manually. This may be due to the version being a post-release version (e.g. 1.0.0.post1) or a pre-release version (e.g. 1.0.0a1). READ THE CHANGELOG BEFORE UPDATING. NEW VERSIONS MAY BREAK YOUR CODE IF NOT PROPERLY UPDATED."
                        )
                    else:
                        logging.critical(
                            f"Whatsapp-python is out of date. Please update to the latest version {latest}. READ THE CHANGELOG BEFORE UPDATING. NEW VERSIONS MAY BREAK YOUR CODE IF NOT PROPERLY UPDATED."
                        )

        if token == "":
            logging.error("Token not provided")
            raise ValueError("Token not provided but required")
        if phone_number_id == "":
            logging.error("Phone number ID not provided")
            raise ValueError("Phone number ID not provided but required")
        self.token = token
        self.phone_number_id = phone_number_id
        self.base_url = f"https://graph.facebook.com/{self.LATEST}"
        self.url = f"{self.base_url}/{phone_number_id}/messages"
        self.verify_token = verify_token

        async def base(*args):
            pass

        self.message_handler = base
        self.other_handler = base
        self.verification_handler = base
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }
        if logger is False:
            logging.disable(logging.INFO)
            logging.disable(logging.ERROR)
        if debug is False:
            logging.disable(logging.DEBUG)
            logging.disable(logging.ERROR)

        self.app = FastAPI()

        # Verification handler has 1 argument: challenge (str | bool): str if verification is successful, False if not

        @self.app.get("/")
        async def verify_endpoint(r: Request):
            if r.query_params.get("hub.verify_token") == self.verify_token:
                logging.debug("Webhook verified successfully")
                challenge = r.query_params.get("hub.challenge")
                await self.verification_handler(challenge)
                await self.other_handler(challenge)
                return int(challenge)
            logging.error("Webhook Verification failed - token mismatch")
            await self.verification_handler(False)
            await self.other_handler(False)
            return {"success": False}

        @self.app.post("/")
        async def hook(r: Request):
            try:
                # Handle Webhook Subscriptions
                data = await r.json()
                if data is None:
                    return {"success": False}
                data_str = json.dumps(data, indent=4)
                # log the data received only if the log level is debug
                logging.debug(f"Received webhook data: {data_str}")

                changed_field = self.changed_field(data)
                if changed_field == "messages":
                    new_message = self.is_message(data)
                    if new_message:
                        msg = Message(instance=self, data=data)
                        await self.message_handler(msg)
                        await self.other_handler(msg)
                return {"success": True}
            except Exception as e:
                logging.error(f"Error parsing message: {e}")
                raise HTTPException(
                    status_code=500, detail={"success": False, "error": str(e)}
                )

    # all the files starting with _ are imported here, and should not be imported directly.

    is_message = staticmethod(is_message)
    get_mobile = staticmethod(get_mobile)
    get_name = staticmethod(get_name)
    get_message = staticmethod(get_message)
    get_message_id = staticmethod(get_message_id)
    get_message_type = staticmethod(get_message_type)
    get_message_timestamp = staticmethod(get_message_timestamp)
    get_audio = staticmethod(get_audio)
    get_delivery = staticmethod(get_delivery)
    get_document = staticmethod(get_document)
    get_image = staticmethod(get_image)
    get_sticker = staticmethod(get_sticker)
    get_interactive_response = staticmethod(get_interactive_response)
    get_location = staticmethod(get_location)
    get_video = staticmethod(get_video)
    changed_field = staticmethod(changed_field)
    get_author = staticmethod(get_author)

    send_button = async_send_button
    create_button = async_create_button
    send_reply_button = async_send_reply_button
    send_image = async_send_image
    send_video = async_send_video
    send_audio = async_send_audio
    send_location = async_send_location
    send_sticker = async_send_sticker
    send_document = async_send_document
    upload_media = async_upload_media
    query_media_url = async_query_media_url
    download_media = async_download_media
    delete_media = async_delete_media
    send_template = async_send_template
    send_custom_json = async_send_custom_json
    send_contacts = async_send_contacts
    authorized = property(async_authorized)
    
    def handle(self, data: dict):
        return Handle(data)

    def create_message(self, **kwargs) -> AsyncMessage:
        """
        Create a message object

        Args:
            data[dict]: The message data
            content[str]: The message content
            to[str]: The recipient
            rec_type[str]: The recipient type (individual/group)
        """
        return AsyncMessage(**kwargs, instance=self)

    def on_message(self, handler: function):
        """
        Set the handler for incoming messages

        Args:
            handler[function]: The handler function
        """
        self.message_handler = handler

    def on_event(self, handler: function):
        """
        Set the handler for other events

        Args:
            handler[function]: The handler function
        """
        self.other_handler = handler

    def on_verification(self, handler: function):
        """
        Set the handler for verification

        Args:
            handler[function]: The handler function
        """
        self.verification_handler = handler

    def run(self, host: str = "localhost", port: int = 5000, **options):
        _run(self.app, host=host, port=port, **options)


class Message:
    # type: ignore
    def __init__(
        self,
        id: int = None,
        data: dict = {},
        instance: WhatsApp = None,
        content: str = "",
        to: str = "",
        rec_type: str = "individual",
    ):
        self.instance = instance
        self.url = self.instance.url
        self.headers = self.instance.headers

        try:
            self.id = instance.get_message_id(data)
        except:
            self.id = id
        try:
            self.type = self.instance.get_message_type(data)
        except:
            self.type = "text"
        self.data = data
        self.rec = rec_type
        self.to = to
        try:
            self.content = content if content != "" else self.instance.get_message(data)
        except:
            self.content = content
        try:
            self.name = self.instance.get_name(data)
        except:
            self.name = None

        if self.type == "image":
            try:
                self.image = self.instance.get_image(data)
            except:
                self.image = None
        if self.type == "sticker":
            try:
                self.sticker = self.instance.get_sticker(data)
            except:
                self.sticker = None
        elif self.type == "video":
            try:
                self.video = self.instance.get_video(data)
            except:
                self.video = None
        elif self.type == "audio":
            try:
                self.audio = self.instance.get_audio(data)
            except:
                pass
        elif self.type == "document":
            try:
                self.document = self.instance.get_document(data)
            except:
                pass
        elif self.type == "location":
            try:
                self.location = self.instance.get_location(data)
            except:
                pass
        elif self.type == "interactive":
            try:
                self.interactive = self.instance.get_interactive_response(data)
            except:
                pass

    def reply(self, reply_text: str = "", preview_url: bool = True) -> dict:
        if self.data == {}:
            return {"error": "No data provided"}
        author = self.instance.get_author(self.data)
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": str(author),
            "type": "text",
            "context": {"message_id": self.id},
            "text": {"preview_url": preview_url, "body": reply_text},
        }
        logging.info(f"Replying to {self.id}")
        r = requests.post(self.url, headers=self.headers, json=payload)
        if r.status_code == 200:
            logging.info(f"Message sent to {self.instance.get_author(self.data)}")
            return r.json()
        logging.info(f"Message not sent to {self.instance.get_author(self.data)}")
        logging.info(f"Status code: {r.status_code}")
        logging.error(f"Response: {r.json()}")
        return r.json()

    def mark_as_read(self) -> dict:

        payload = {
            "messaging_product": "whatsapp",
            "status": "read",
            "message_id": self.id,
        }

        response = requests.post(
            f"{self.instance.url}", headers=self.instance.headers, json=payload
        )
        if response.status_code == 200:
            logging.info(response.json())
            return response.json()
        else:
            logging.error(response.json())
            return response.json()

    def send(self, sender=None, preview_url: bool = True) -> dict:

        try:
            sender = dict(self.instance.l)[sender]

        except:
            sender = self.instance.phone_number_id

        if sender == None:
            sender = self.instance.phone_number_id

        url = f"https://graph.facebook.com/{self.instance.LATEST}/{sender}/messages"
        data = {
            "messaging_product": "whatsapp",
            "recipient_type": self.rec,
            "to": self.to,
            "type": "text",
            "text": {"preview_url": preview_url, "body": self.content},
        }
        logging.info(f"Sending message to {self.to}")
        r = requests.post(url, headers=self.headers, json=data)
        if r.status_code == 200:
            logging.info(f"Message sent to {self.to}")
            return r.json()
        logging.info(f"Message not sent to {self.to}")
        logging.info(f"Status code: {r.status_code}")
        logging.error(f"Response: {r.json()}")
        return r.json()

    def react(self, emoji: str) -> dict:
        data = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": self.to,
            "type": "reaction",
            "reaction": {"message_id": self.id, "emoji": emoji},
        }
        logging.info(f"Reacting to {self.id}")
        r = requests.post(self.url, headers=self.headers, json=data)
        if r.status_code == 200:
            logging.info(f"Reaction sent to {self.to}")
            return r.json()
        logging.info(f"Reaction not sent to {self.to}")
        logging.info(f"Status code: {r.status_code}")
        logging.debug(f"Response: {r.json()}")
        return r.json()


class AsyncMessage:
    # type: ignore
    def __init__(
        self,
        id: int = None,
        data: dict = {},
        instance: WhatsApp = None,
        content: str = "",
        to: str = "",
        rec_type: str = "individual",
    ):
        self.instance = instance
        self.url = self.instance.url
        self.headers = self.instance.headers

        try:
            self.id = instance.get_message_id(data)
        except:
            self.id = id
        try:
            self.type = self.instance.get_message_type(data)
        except:
            self.type = "text"
        self.data = data
        self.rec = rec_type
        self.to = to
        try:
            self.content = content if content != "" else self.instance.get_message(data)
        except:
            self.content = content
        try:
            self.name = self.instance.get_name(data)
        except:
            self.name = None

        if self.type == "image":
            try:
                self.image = self.instance.get_image(data)
            except:
                self.image = None
        if self.type == "sticker":
            try:
                self.sticker = self.instance.get_sticker(data)
            except:
                self.sticker = None
        elif self.type == "video":
            try:
                self.video = self.instance.get_video(data)
            except:
                self.video = None
        elif self.type == "audio":
            try:
                self.audio = self.instance.get_audio(data)
            except:
                pass
        elif self.type == "document":
            try:
                self.document = self.instance.get_document(data)
            except:
                pass
        elif self.type == "location":
            try:
                self.location = self.instance.get_location(data)
            except:
                pass
        elif self.type == "interactive":
            try:
                self.interactive = self.instance.get_interactive_response(data)
            except:
                pass

    async def reply(
        self, reply_text: str = "", preview_url: bool = True
    ) -> asyncio.Future:
        if self.data == {}:
            return {"error": "No data provided"}
        author = self.instance.get_author(self.data)
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": str(author),
            "type": "text",
            "context": {"message_id": self.id},
            "text": {"preview_url": preview_url, "body": reply_text},
        }
        logging.info(f"Replying to {self.id}")

        async def call():
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.url, headers=self.headers, json=payload
                ) as response:
                    if response.status == 200:
                        logging.info(
                            f"Message sent to {self.instance.get_author(self.data)}"
                        )
                        return await response.json()
                    logging.info(
                        f"Message not sent to {self.instance.get_author(self.data)}"
                    )
                    logging.info(f"Status code: {response.status}")
                    logging.error(f"Response: {await response.json()}")
                    return await response.json()

        f = asyncio.ensure_future(call())
        await asyncio.sleep(0.001)  # make asyncio run the task
        return f

    async def mark_as_read(self) -> asyncio.Future:

        payload = {
            "messaging_product": "whatsapp",
            "status": "read",
            "message_id": self.id,
        }

        async def call():
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.instance.url}", headers=self.instance.headers, json=payload
                ) as response:
                    if response.status == 200:
                        logging.info(await response.json())
                        return await response.json()
                    else:
                        logging.error(await response.json())
                        return await response.json()

        f = asyncio.ensure_future(call())
        await asyncio.sleep(0.001)  # make asyncio run the task
        return f

    async def send(self, sender=None, preview_url: bool = True) -> asyncio.Future:
        try:
            sender = dict(self.instance.l)[sender]

        except:
            sender = self.instance.phone_number_id

        if sender == None:
            sender = self.instance.phone_number_id

        url = f"https://graph.facebook.com/{self.instance.LATEST}/{sender}/messages"
        data = {
            "messaging_product": "whatsapp",
            "recipient_type": self.rec,
            "to": self.to,
            "type": "text",
            "text": {"preview_url": preview_url, "body": self.content},
        }
        logging.info(f"Sending message to {self.to}")

        async def call():
            print("sending")
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, headers=self.headers, json=data
                ) as response:
                    if response.status == 200:
                        logging.info(f"Message sent to {self.to}")
                        return await response.json()
                    logging.info(f"Message not sent to {self.to}")
                    logging.info(f"Status code: {response.status}")
                    logging.error(f"Response: {await response.json()}")
                    return await response.json()

        f = asyncio.ensure_future(call())
        await asyncio.sleep(0.001)  # make asyncio run the task
        return f

    async def react(self, emoji: str) -> asyncio.Future:
        data = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": self.to,
            "type": "reaction",
            "reaction": {"message_id": self.id, "emoji": emoji},
        }
        logging.info(f"Reacting to {self.id}")

        async def call():
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.url, headers=self.headers, json=data
                ) as response:
                    if response.status == 200:
                        logging.info(f"Reaction sent to {self.to}")
                        return await response.json()
                    logging.info(f"Reaction not sent to {self.to}")
                    logging.info(f"Status code: {response.status}")
                    logging.debug(f"Response: {await response.json()}")
                    return await response.json()

        f = asyncio.ensure_future(call())
        await asyncio.sleep(0.001)
        return f

```



## Source: whatsapp/constants.py
```
# This file contains the constants used in the project.
# The VERSION constant is used to store the version of the project - it's not only used in the __init__.py file, but also in the pyproject.toml file.

VERSION = "4.3.0"

```



## Source: whatsapp/errors.py
```
from typing import Union


class Handler:
    def __init__(self, error: dict):
        self.error = error


class AuthException(Exception):  # invoked by code 0
    pass


class MethodException(AuthException):  # invoked by code 3
    pass


class ForbiddenException(AuthException):  # invoked by codes 10, 200-299
    pass


class ExpiredTokenException(AuthException):  # invoked by code 190
    pass


class ThrottlingException(Exception):  # Category exception
    pass


class RateLimitException(ThrottlingException):  # invoked by code 4
    pass


class UserRateLimitException(ThrottlingException):  # invoked by code 80007
    pass


class AppRateLimitException(ThrottlingException):  # invoked by code 130429
    pass


class SpamException(ThrottlingException):  # invoked by code 131048
    pass


class CoupleRateLimitException(ThrottlingException):  # invoked by code 131056
    pass


class RegistrationRateLimitException(ThrottlingException):  # invoked by code 133016
    pass


class IntegrityException(Exception):  # Category exception
    pass


class RulesViolationException(IntegrityException):  # invoked by code 368
    pass


class GeoRestrictionException(IntegrityException):  # invoked by code 130497
    pass


class BlockedAccountException(IntegrityException):  # invoked by code 131031
    pass


# Generic exceptions
class UnknownAPIException(Exception):  # invoked by code 1
    pass


class ServiceUnavailableException(Exception):  # invoked by code 2
    pass


class WrongPhoneNumberException(Exception):  # invoked by code 33
    pass


class InvalidParameterException(Exception):  # invoked by code 100
    pass


class ExperimentalNumberException(Exception):  # invoked by code 130472
    pass


class UnknownErrorException(Exception):  # invoked by code 131000
    pass


class AccessDeniedException(Exception):  # invoked by code 131005
    pass


class RequiredParameterMissingException(Exception):  # invoked by code 131008
    pass


class InvalidParameterTypeException(Exception):  # invoked by code 131009
    pass


class ServiceUnavailableException(Exception):  # invoked by code 131016
    pass


class SamePhoneNumberException(Exception):  # invoked by code 131021
    pass


class DeliveryFailureException(Exception):  # invoked by code 131026
    pass


class PaymentFailureException(Exception):  # invoked by code 131042
    pass


class PhoneRegistrationFailureException(Exception):  # invoked by code 131045
    pass


class ChatExpiredException(Exception):  # invoked by code 131047
    pass


class MetaDeliveryFailureException(Exception):  # invoked by code 131049
    pass


class UnsupportedMessageTypeException(Exception):  # invoked by code 131051
    pass


class MediaDownloadFailureException(Exception):  # invoked by code 131052
    pass


class MediaLoadFailureException(Exception):  # invoked by code 131053
    pass


class MaintenanceException(Exception):  # invoked by code 131057
    pass


class ParameterNumberMismatchException(Exception):  # invoked by code 132000
    pass


class ModelNotFoundException(Exception):  # invoked by code 132001
    pass


class TextTooLongException(Exception):  # invoked by code 132005
    pass


class CharacterFormatException(Exception):  # invoked by code 132007
    pass


class WrongParameterFormatException(Exception):  # invoked by code 132012
    pass


class PausedTemplateException(Exception):  # invoked by code 132015
    pass


class DisabledTemplateException(Exception):  # invoked by code 132016
    pass


class StreamBlockedException(Exception):  # invoked by code 132068
    pass


class StreamThrottlingException(Exception):  # invoked by code 132069
    pass


class FailedToRevertRegistrationException(Exception):  # invoked by code 133000
    pass


class ServerTemporaryUnavailableException(Exception):  # invoked by code 133004
    pass


class MFAPinIncorrectException(Exception):  # invoked by code 133005
    pass


class PhoneVerificationRequiredException(Exception):  # invoked by code 133006
    pass


class TooManyPinAttemptsException(Exception):  # invoked by code 133008
    pass


class TooFastPinAttemptsException(Exception):  # invoked by code 133009
    pass


class UnregisteredNumberException(Exception):  # invoked by code 133010
    pass


class RetryLaterException(Exception):  # invoked by code 133015
    pass


class GenericUserException(Exception):  # invoked by code 135000
    pass


pairings = {
    0: AuthException,
    1: UnknownAPIException,
    2: ServiceUnavailableException,
    3: MethodException,
    4: RateLimitException,
    10: ForbiddenException,
    33: WrongPhoneNumberException,
    100: InvalidParameterException,
    200: ForbiddenException,
    130429: AppRateLimitException,
    130472: ExperimentalNumberException,
    130497: GeoRestrictionException,
    131000: UnknownErrorException,
    131005: AccessDeniedException,
    131008: RequiredParameterMissingException,
    131009: InvalidParameterTypeException,
    131016: ServiceUnavailableException,
    131021: SamePhoneNumberException,
    131026: DeliveryFailureException,
    131042: PaymentFailureException,
    131045: PhoneRegistrationFailureException,
    131047: ChatExpiredException,
    131048: SpamException,
    131049: MetaDeliveryFailureException,
    131051: UnsupportedMessageTypeException,
    131052: MediaDownloadFailureException,
    131053: MediaLoadFailureException,
    131056: CoupleRateLimitException,
    131057: MaintenanceException,
    132000: ParameterNumberMismatchException,
    132001: ModelNotFoundException,
    132005: TextTooLongException,
    132007: CharacterFormatException,
    132012: WrongParameterFormatException,
    132015: PausedTemplateException,
    132016: DisabledTemplateException,
    132068: StreamBlockedException,
    132069: StreamThrottlingException,
    133000: FailedToRevertRegistrationException,
    133004: ServerTemporaryUnavailableException,
    133005: MFAPinIncorrectException,
    133006: PhoneVerificationRequiredException,
    133008: TooManyPinAttemptsException,
    133009: TooFastPinAttemptsException,
    133010: UnregisteredNumberException,
    133015: RetryLaterException,
    80007: UserRateLimitException,
    131031: BlockedAccountException,
    131056: CoupleRateLimitException,
    131057: MaintenanceException,
    133016: RegistrationRateLimitException,
    368: RulesViolationException,
    190: ExpiredTokenException,
}


class Handle:
    def __init__(self, data: dict) -> Union[Exception, None]:
        try:
            code = data["error"]["code"]
            if code in pairings:
                raise pairings[code]({"error": data["error"]["message"], "code": code})
            else: 
                if code in range(200, 300):
                    raise ForbiddenException({"error": data["error"]["message"], "code": code})
            raise Handler(Exception({"error": data["error"]["message"], "code": code}))
        except KeyError:
            return None
```



## Examples Directory
The following examples demonstrate how to use the library:

### Example: reply_to_message_obj.py
```
from os import getenv
from whatsapp import WhatsApp, Message
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    data = {"your": "data"}

    msg = Message(data=data, instance=messenger, sender=1)
    msg.reply("lol")

    print(msg)

```



### Example: sending_audio.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_audio(
        audio="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
        recipient_id="255757xxxxxx",
        sender=1,
    )

    print(response)

```



### Example: sending_button.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_button(
        recipient_id="255757xxxxxx",
        button={
            "header": "Header Testing",
            "body": "Body Testing",
            "footer": "Footer Testing",
            "action": {
                "button": "Button Testing",
                "sections": [
                    {
                        "title": "iBank",
                        "rows": [
                            {"id": "row 1", "title": "Send Money", "description": ""},
                            {
                                "id": "row 2",
                                "title": "Withdraw money",
                                "description": "",
                            },
                        ],
                    }
                ],
            },
        },
        sender=1,
    )

```



### Example: sending_button_async.py
```
from os import getenv
from whatsapp import AsyncWhatsApp, AsyncMessage
import asyncio
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = AsyncWhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    async def run_test():
        response = await messenger.send_button(
            recipient_id="255757xxxxxx",
            button={
                "header": "Header Testing",
                "body": "Body Testing",
                "footer": "Footer Testing",
                "action": {
                    "button": "Button Testing",
                    "sections": [
                        {
                            "title": "iBank",
                            "rows": [
                                {"id": "row 1", "title": "Send Money", "description": ""},
                                {
                                    "id": "row 2",
                                    "title": "Withdraw money",
                                    "description": "",
                                },
                            ],
                        }
                    ],
                },
            },
            sender=1,
        )
        while not response.done():
            await asyncio.sleep(.1)
        print(response.result())
        
    asyncio.run(run_test())

```



### Example: sending_document.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()

    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_document(
        document="http://www.africau.edu/images/default/sample.pdf",
        recipient_id="255757294146",
        sender=1,
    )

    print(response)

```



### Example: sending_image.py
```



from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"),
                         phone_number_id={1:"1234", 2: "5678"})

    response = messenger.send_image(
        image="https://i.imgur.com/Fh7XVYY.jpeg",
        recipient_id="255757294146",
        sender=1
    )

    print(response)

```



### Example: sending_location.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_location(
        lat=1.29,
        long=103.85,
        name="Singapore",
        address="Singapore",
        recipient_id="255757294146",
        sender=1,
    )

    print(response)

```



### Example: sending_message.py
```
from os import getenv
from whatsapp import WhatsApp, Message
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    msg = Message(
        instance=messenger, content="Hello World!", to="919999999999", sender=1
    )
    response = msg.send()

    print(response)

```



### Example: sending_message_async.py
```
from os import getenv
from whatsapp import AsyncWhatsApp, AsyncMessage
import asyncio
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = AsyncWhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    msg = AsyncMessage(
        instance=messenger, content="Hello World!", to="919999999999", sender=1
    )
    async def run_test():
        response = await msg.send()
        while not response.done():
            await asyncio.sleep(.1)
        print(response.result())
        
    asyncio.run(run_test())

```



### Example: sending_template_message.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_template(
        "hello_world", "255757xxxxxx", components=[], lang="en_US", sender=1
    )

    print(response)

```



### Example: sending_video.py
```
from os import getenv
from whatsapp import WhatsApp
from dotenv import load_dotenv

if __name__ == "__main__":
    load_dotenv()
    messenger = WhatsApp(token=getenv("TOKEN"), phone_number_id={1: "1234", 2: "5678"})

    response = messenger.send_video(
        video="https://www.youtube.com/watch?v=K4TOrB7at0Y",
        recipient_id="255757xxxxxx",
        sender=1,
    )

    print(response)

```



### Example: standalone_hook.py
```
import os
import logging
from whatsapp import WhatsApp, Message
from dotenv import load_dotenv
from flask import Flask, request, Response

# Initialize Flask App
app = Flask(__name__)

# Load .env file
load_dotenv("../.env")
messenger = WhatsApp(os.getenv("TOKEN"), phone_number_id=os.getenv("ID"))
VERIFY_TOKEN = "30cca545-3838-48b2-80a7-9e43b1ae8ce4"

# Logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


@app.get("/")
def verify_token():
    if request.args.get("hub.verify_token") == VERIFY_TOKEN:
        logging.info("Verified webhook")
        challenge = request.args.get("hub.challenge")
        return str(challenge)
    logging.error("Webhook Verification failed")
    return "Invalid verification token"


@app.post("/")
def hook():
    # Handle Webhook Subscriptions
    data = request.get_json()
    if data is None:
        return Response(status=200)
    logging.info("Received webhook data: %s", data)
    changed_field = messenger.changed_field(data)
    if changed_field == "messages":
        new_message = messenger.is_message(data)
        if new_message:
            msg = Message(instance=messenger, data=data)
            mobile = msg.sender
            name = msg.name
            message_type = msg.type
            logging.info(
                f"New Message; sender:{mobile} name:{name} type:{message_type}"
            )
            if message_type == "text":
                message = msg.content
                name = msg.name
                logging.info("Message: %s", message)
                m = Message(instance=messenger, to=mobile, content="Hello World")
                m.send()

            elif message_type == "interactive":
                message_response = msg.interactive
                if message_response is None:
                    return Response(status=400)
                interactive_type = message_response.get("type")
                message_id = message_response[interactive_type]["id"]
                message_text = message_response[interactive_type]["title"]
                logging.info(f"Interactive Message; {message_id}: {message_text}")

            elif message_type == "location":
                message_location = msg.location
                if message_location is None:
                    return Response(status=400)
                message_latitude = message_location["latitude"]
                message_longitude = message_location["longitude"]
                logging.info("Location: %s, %s", message_latitude, message_longitude)

            elif message_type == "image":
                image = msg.image
                if image is None:
                    return Response(status=400)
                image_id, mime_type = image["id"], image["mime_type"]
                image_url = messenger.query_media_url(image_id)
                if image_url is None:
                    return Response(status=400)
                image_filename = messenger.download_media(image_url, mime_type)
                logging.info(f"{mobile} sent image {image_filename}")

            elif message_type == "sticker":
                sticker = msg.sticker
                if sticker is None:
                    return Response(status=400)
                sticker_id, mime_type = sticker["id"], sticker["mime_type"]
                sticker_url = messenger.query_media_url(sticker_id)
                if sticker_url is None:
                    return Response(status=400)
                sticker_filename = messenger.download_media(sticker_url, mime_type)
                logging.info(f"{mobile} sent sticker {sticker_filename}")

            elif message_type == "video":
                video = msg.video
                if video is None:
                    return Response(status=400)
                video_id, mime_type = video["id"], video["mime_type"]
                video_url = messenger.query_media_url(video_id)
                if video_url is None:
                    return Response(status=400)
                video_filename = messenger.download_media(video_url, mime_type)
                logging.info(f"{mobile} sent video {video_filename}")

            elif message_type == "audio":
                audio = msg.audio
                if audio is None:
                    return Response(status=400)
                audio_id, mime_type = audio["id"], audio["mime_type"]
                audio_url = messenger.query_media_url(audio_id)
                if audio_url is None:
                    return Response(status=400)
                audio_filename = messenger.download_media(audio_url, mime_type)
                logging.info(f"{mobile} sent audio {audio_filename}")

            elif message_type == "document":
                file = msg.document
                if file is None:
                    return Response(status=400)
                file_id, mime_type = file["id"], file["mime_type"]
                file_url = messenger.query_media_url(file_id)
                if file_url is None:
                    return Response(status=400)
                file_filename = messenger.download_media(file_url, mime_type)
                logging.info(f"{mobile} sent file {file_filename}")
            else:
                logging.info(f"{mobile} sent {message_type} ")
                logging.info(data)
        else:
            delivery = messenger.get_delivery(data)
            if delivery:
                logging.info(f"Message : {delivery}")
            else:
                logging.info("No new message")
    return "OK", 200


if __name__ == "__main__":
    app.run(port=6869, debug=False)

```



## Async Extensions Directory
[Skip to content](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/async_ext#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Fwhatsapp%2Fasync_ext)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Fwhatsapp%2Fasync_ext)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Ffiles%2Fdisambiguate&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/async_ext) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/async_ext) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/async_ext) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/async_ext).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


## Collapse file tree
## Files
main
Search this repository
  * .github
  * examples
  * tests
  * whatsapp
    * async_ext
      * _buttons.py
      * _media.py
      * _message.py
      * _property.py
      * _send_media.py
      * _send_others.py
      * _static.py
    * ext
    * __init__.py
    * constants.py
    * errors.py
  * .gitignore
  * CODE_OF_CONDUCT.md
  * CONTRIBUTING.md
  * LICENSE
  * README.md
  * SECURITY.md
  * pyproject.toml


## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[whatsapp](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)


/
# async_ext
/
Copy path
## Directory actions
## More options
More options
## Directory actions
## More options
More options
## Latest commit
## History
[History](https://github.com/filipporomani/whatsapp-python/commits/main/whatsapp/async_ext)
[](https://github.com/filipporomani/whatsapp-python/commits/main/whatsapp/async_ext)
## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[whatsapp](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)


/
# async_ext
/
Top
## Folders and files
Name | Name | Last commit message | Last commit date  
---|---|---|---  
### parent directory
[](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)  
[_buttons.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_buttons.py "_buttons.py") | [_buttons.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_buttons.py "_buttons.py") |  |   
[_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_media.py "_media.py") | [_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_media.py "_media.py") |  |   
[_message.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_message.py "_message.py") | [_message.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_message.py "_message.py") |  |   
[_property.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_property.py "_property.py") | [_property.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_property.py "_property.py") |  |   
[_send_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_send_media.py "_send_media.py") | [_send_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_send_media.py "_send_media.py") |  |   
[_send_others.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_send_others.py "_send_others.py") | [_send_others.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_send_others.py "_send_others.py") |  |   
[_static.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_static.py "_static.py") | [_static.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/async_ext/_static.py "_static.py") |  |   
View all files  
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Extensions Directory
[Skip to content](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/ext#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Fwhatsapp%2Fext)
Appearance settings
  * Product 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Fwhatsapp%2Fext)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Ffiles%2Fdisambiguate&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/ext) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/ext) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/ext) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp/ext).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


## Collapse file tree
## Files
main
Search this repository
  * .github
  * examples
  * tests
  * whatsapp
    * async_ext
    * ext
      * _buttons.py
      * _media.py
      * _message.py
      * _property.py
      * _send_media.py
      * _send_others.py
      * _static.py
    * __init__.py
    * constants.py
    * errors.py
  * .gitignore
  * CODE_OF_CONDUCT.md
  * CONTRIBUTING.md
  * LICENSE
  * README.md
  * SECURITY.md
  * pyproject.toml


## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[whatsapp](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)


/
# ext
/
Copy path
## Directory actions
## More options
More options
## Directory actions
## More options
More options
## Latest commit
[![filipporomani](https://avatars.githubusercontent.com/u/72656809?v=4&size=40)](https://github.com/filipporomani)[filipporomani](https://github.com/filipporomani/whatsapp-python/commits?author=filipporomani)
[feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817)
Nov 25, 2024
[f6cf72a](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817) · Nov 25, 2024
## History
[History](https://github.com/filipporomani/whatsapp-python/commits/main/whatsapp/ext)
Open commit details
[](https://github.com/filipporomani/whatsapp-python/commits/main/whatsapp/ext)
## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[whatsapp](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)


/
# ext
/
Top
## Folders and files
Name | Name | Last commit message | Last commit date  
---|---|---|---  
### parent directory
[](https://github.com/filipporomani/whatsapp-python/tree/main/whatsapp)  
[_buttons.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_buttons.py "_buttons.py") | [_buttons.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_buttons.py "_buttons.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_media.py "_media.py") | [_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_media.py "_media.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[_message.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_message.py "_message.py") | [_message.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_message.py "_message.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[_property.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_property.py "_property.py") | [_property.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_property.py "_property.py") | [refactor: pep8](https://github.com/filipporomani/whatsapp-python/commit/582ec32da389a867111b2090580de89494b7155c "refactor: pep8") | May 28, 2023  
[_send_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_send_media.py "_send_media.py") | [_send_media.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_send_media.py "_send_media.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[_send_others.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_send_others.py "_send_others.py") | [_send_others.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_send_others.py "_send_others.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[_static.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_static.py "_static.py") | [_static.py](https://github.com/filipporomani/whatsapp-python/blob/main/whatsapp/ext/_static.py "_static.py") | [fix: format py files](https://github.com/filipporomani/whatsapp-python/commit/6fd7b0752a350163137056d0cea2d064dc1522ec "fix: format py files") | Nov 16, 2024  
View all files  
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## Tests Directory
[Skip to content](https://github.com/filipporomani/whatsapp-python/tree/main/tests#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Ftests)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2Ftests)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Ffiles%2Fdisambiguate&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/tests) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/tests) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/tests) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/tree/main/tests).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


## Collapse file tree
## Files
main
Search this repository
  * .github
  * examples
  * tests
    * async_tests.py
    * tests.py
  * whatsapp
  * .gitignore
  * CODE_OF_CONDUCT.md
  * CONTRIBUTING.md
  * LICENSE
  * README.md
  * SECURITY.md
  * pyproject.toml


## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)


/
# tests
/
Copy path
## Directory actions
## More options
More options
## Directory actions
## More options
More options
## Latest commit
[![filipporomani](https://avatars.githubusercontent.com/u/72656809?v=4&size=40)](https://github.com/filipporomani)[filipporomani](https://github.com/filipporomani/whatsapp-python/commits?author=filipporomani)
[feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817)
Nov 25, 2024
[f6cf72a](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817) · Nov 25, 2024
## History
[History](https://github.com/filipporomani/whatsapp-python/commits/main/tests)
Open commit details
[](https://github.com/filipporomani/whatsapp-python/commits/main/tests)
## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)


/
# tests
/
Top
## Folders and files
Name | Name | Last commit message | Last commit date  
---|---|---|---  
### parent directory
[](https://github.com/filipporomani/whatsapp-python/tree/main)  
[async_tests.py](https://github.com/filipporomani/whatsapp-python/blob/main/tests/async_tests.py "async_tests.py") | [async_tests.py](https://github.com/filipporomani/whatsapp-python/blob/main/tests/async_tests.py "async_tests.py") | [feat: full error handling implementation](https://github.com/filipporomani/whatsapp-python/commit/f6cf72a986ae5652bc22b5474bffced1f33cd817 "feat: full error handling implementation") | Nov 25, 2024  
[tests.py](https://github.com/filipporomani/whatsapp-python/blob/main/tests/tests.py "tests.py") | [tests.py](https://github.com/filipporomani/whatsapp-python/blob/main/tests/tests.py "tests.py") | [improve file structue](https://github.com/filipporomani/whatsapp-python/commit/bff5e3869a1d8f9f5f1767c78ec30f0aecf5358d "improve file structue") | Nov 23, 2024  
View all files  
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information 




## GitHub Workflows
[Skip to content](https://github.com/filipporomani/whatsapp-python/tree/main/.github/workflows#start-of-content)
## Navigation Menu
Toggle navigation
[ ](https://github.com/)
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2F.github%2Fworkflows)
Appearance settings
  * Platform 
    * [ GitHub Copilot  Write better code with AI  ](https://github.com/features/copilot)
    * [ GitHub Spark  New  Build and deploy intelligent apps  ](https://github.com/features/spark)
    * [ GitHub Models  New  Manage and compare prompts  ](https://github.com/features/models)
    * [ GitHub Advanced Security  Find and fix vulnerabilities  ](https://github.com/security/advanced-security)
    * [ Actions  Automate any workflow  ](https://github.com/features/actions)
    * [ Codespaces  Instant dev environments  ](https://github.com/features/codespaces)
    * [ Issues  Plan and track work  ](https://github.com/features/issues)
    * [ Code Review  Manage code changes  ](https://github.com/features/code-review)
    * [ Discussions  Collaborate outside of code  ](https://github.com/features/discussions)
    * [ Code Search  Find more, search less  ](https://github.com/features/code-search)
Explore
    * [ Why GitHub ](https://github.com/why-github)
    * [ All features ](https://github.com/features)
    * [ Documentation ](https://docs.github.com)
    * [ GitHub Skills ](https://skills.github.com)
    * [ Blog ](https://github.blog)
  * Solutions 
By company size
    * [ Enterprises ](https://github.com/enterprise)
    * [ Small and medium teams ](https://github.com/team)
    * [ Startups ](https://github.com/enterprise/startups)
    * [ Nonprofits ](https://github.com/solutions/industry/nonprofits)
By use case
    * [ DevSecOps ](https://github.com/solutions/use-case/devsecops)
    * [ DevOps ](https://github.com/solutions/use-case/devops)
    * [ CI/CD ](https://github.com/solutions/use-case/ci-cd)
    * [ View all use cases ](https://github.com/solutions/use-case)
By industry
    * [ Healthcare ](https://github.com/solutions/industry/healthcare)
    * [ Financial services ](https://github.com/solutions/industry/financial-services)
    * [ Manufacturing ](https://github.com/solutions/industry/manufacturing)
    * [ Government ](https://github.com/solutions/industry/government)
    * [ View all industries ](https://github.com/solutions/industry)
[ View all solutions ](https://github.com/solutions)
  * Resources 
Topics
    * [ AI ](https://github.com/resources/articles/ai)
    * [ DevOps ](https://github.com/resources/articles/devops)
    * [ Security ](https://github.com/resources/articles/security)
    * [ Software Development ](https://github.com/resources/articles/software-development)
    * [ View all ](https://github.com/resources/articles)
Explore
    * [ Learning Pathways ](https://resources.github.com/learn/pathways)
    * [ Events & Webinars ](https://github.com/resources/events)
    * [ Ebooks & Whitepapers ](https://github.com/resources/whitepapers)
    * [ Customer Stories ](https://github.com/customer-stories)
    * [ Partners ](https://partner.github.com)
    * [ Executive Insights ](https://github.com/solutions/executive-insights)
  * Open Source 
    * [ GitHub Sponsors  Fund open source developers  ](https://github.com/sponsors)
    * [ The ReadME Project  GitHub community articles  ](https://github.com/readme)
Repositories
    * [ Topics ](https://github.com/topics)
    * [ Trending ](https://github.com/trending)
    * [ Collections ](https://github.com/collections)
  * Enterprise 
    * [ Enterprise platform  AI-powered developer platform  ](https://github.com/enterprise)
Available add-ons
    * [ GitHub Advanced Security  Enterprise-grade security features  ](https://github.com/security/advanced-security)
    * [ Copilot for business  Enterprise-grade AI features  ](https://github.com/features/copilot/copilot-business)
    * [ Premium Support  Enterprise-grade 24/7 support  ](https://github.com/premium-support)
  * [Pricing](https://github.com/pricing)


Search or jump to...
# Search code, repositories, users, issues, pull requests...
Search 
Clear
[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)
#  Provide feedback 
We read every piece of feedback, and take your input very seriously.
Include my email address so I can be contacted
Cancel  Submit feedback 
#  Saved searches 
## Use saved searches to filter your results more quickly
Name
Query
To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax). 
Cancel  Create saved search 
[ Sign in ](https://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Ffilipporomani%2Fwhatsapp-python%2Ftree%2Fmain%2F.github%2Fworkflows)
[ Sign up ](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Ffiles%2Fdisambiguate&source=header-repo&source_repo=filipporomani%2Fwhatsapp-python)
Appearance settings
Resetting focus
You signed in with another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/.github/workflows) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/.github/workflows) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/filipporomani/whatsapp-python/tree/main/.github/workflows) to refresh your session. Dismiss alert
{{ message }}
[ filipporomani ](https://github.com/filipporomani) / **[whatsapp-python](https://github.com/filipporomani/whatsapp-python) ** Public
  * [ Sponsor  ](https://github.com/sponsors/filipporomani)
  * [ ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python) You must be signed in to change notification settings
  * [ 23 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)
  * [ Star  141 ](https://github.com/login?return_to=%2Ffilipporomani%2Fwhatsapp-python)


  * [ Code ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues 1 ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests 0 ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects 1 ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
[ ](https://github.com/filipporomani/whatsapp-python/security)
### [ Uh oh!  ](https://github.com/filipporomani/whatsapp-python/security)
[There was an error while loading. ](https://github.com/filipporomani/whatsapp-python/security)[Please reload this page](https://github.com/filipporomani/whatsapp-python/tree/main/.github/workflows).
  * [ Insights ](https://github.com/filipporomani/whatsapp-python/pulse)


Additional navigation options
  * [ Code  ](https://github.com/filipporomani/whatsapp-python)
  * [ Issues  ](https://github.com/filipporomani/whatsapp-python/issues)
  * [ Pull requests  ](https://github.com/filipporomani/whatsapp-python/pulls)
  * [ Discussions  ](https://github.com/filipporomani/whatsapp-python/discussions)
  * [ Actions  ](https://github.com/filipporomani/whatsapp-python/actions)
  * [ Projects  ](https://github.com/filipporomani/whatsapp-python/projects)
  * [ Wiki  ](https://github.com/filipporomani/whatsapp-python/wiki)
  * [ Security  ](https://github.com/filipporomani/whatsapp-python/security)
  * [ Insights  ](https://github.com/filipporomani/whatsapp-python/pulse)


## Collapse file tree
## Files
main
Search this repository
  * .github
    * ISSUE_TEMPLATE
    * workflows
      * codeql.yml
      * dependency-review.yml
      * python-publish.yml
    * FUNDING.yml
    * dependabot.yml
  * examples
  * tests
  * whatsapp
  * .gitignore
  * CODE_OF_CONDUCT.md
  * CONTRIBUTING.md
  * LICENSE
  * README.md
  * SECURITY.md
  * pyproject.toml


## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[.github](https://github.com/filipporomani/whatsapp-python/tree/main/.github)


/
# workflows
/
Copy path
## Directory actions
## More options
More options
## Directory actions
## More options
More options
## Latest commit
[![filipporomani](https://avatars.githubusercontent.com/u/72656809?v=4&size=40)](https://github.com/filipporomani)[filipporomani](https://github.com/filipporomani/whatsapp-python/commits?author=filipporomani)
[revert to cloud runner](https://github.com/filipporomani/whatsapp-python/commit/cca05fa1df16d9a79f14ea7bec16d1f71f6353cd)
Oct 14, 2024
[cca05fa](https://github.com/filipporomani/whatsapp-python/commit/cca05fa1df16d9a79f14ea7bec16d1f71f6353cd) · Oct 14, 2024
## History
[History](https://github.com/filipporomani/whatsapp-python/commits/main/.github/workflows)
Open commit details
[](https://github.com/filipporomani/whatsapp-python/commits/main/.github/workflows)
## Breadcrumbs
  1. [whatsapp-python](https://github.com/filipporomani/whatsapp-python/tree/main)
  2. /[.github](https://github.com/filipporomani/whatsapp-python/tree/main/.github)


/
# workflows
/
Top
## Folders and files
Name | Name | Last commit message | Last commit date  
---|---|---|---  
### parent directory
[](https://github.com/filipporomani/whatsapp-python/tree/main/.github)  
[codeql.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/codeql.yml "codeql.yml") | [codeql.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/codeql.yml "codeql.yml") | [revert to cloud runner](https://github.com/filipporomani/whatsapp-python/commit/cca05fa1df16d9a79f14ea7bec16d1f71f6353cd "revert to cloud runner") | Oct 14, 2024  
[dependency-review.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/dependency-review.yml "dependency-review.yml") | [dependency-review.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/dependency-review.yml "dependency-review.yml") | [revert to cloud runner](https://github.com/filipporomani/whatsapp-python/commit/cca05fa1df16d9a79f14ea7bec16d1f71f6353cd "revert to cloud runner") | Oct 14, 2024  
[python-publish.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/python-publish.yml "python-publish.yml") | [python-publish.yml](https://github.com/filipporomani/whatsapp-python/blob/main/.github/workflows/python-publish.yml "python-publish.yml") | [revert to cloud runner](https://github.com/filipporomani/whatsapp-python/commit/cca05fa1df16d9a79f14ea7bec16d1f71f6353cd "revert to cloud runner") | Oct 14, 2024  
View all files  
## Footer
[ ](https://github.com) © 2025 GitHub, Inc. 
### Footer navigation
  * [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
  * [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
  * [Security](https://github.com/security)
  * [Status](https://www.githubstatus.com/)
  * [Docs](https://docs.github.com/)
  * [Contact](https://support.github.com?tags=dotcom-footer)
  * Manage cookies 
  * Do not share my personal information