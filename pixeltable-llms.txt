# Pixeltable Documentation

## Docs

- [Product Updates](https://docs.pixeltable.com/docs/changelog/product-updates.md): Keep track of changes
- [Bringing Data](https://docs.pixeltable.com/docs/datastore/bringing-data.md): A comprehensive guide to inserting, referencing, and importing data in Pixeltable
- [Computed Columns](https://docs.pixeltable.com/docs/datastore/computed-columns.md): Computed columns combines automatic calculations with smart incremental updates. Think of them as your data workflow automated.
- [Custom Functions (UDFs)](https://docs.pixeltable.com/docs/datastore/custom-functions.md): Create and use custom functions (UDFs) in Pixeltable
- [Building Queries](https://docs.pixeltable.com/docs/datastore/filtering-and-selecting.md): Learn how to query and transform data in Pixeltable using where() and select()
- [Iterators](https://docs.pixeltable.com/docs/datastore/iterators.md): Learn about iterators for processing documents, videos, audio, and images
- [Sampling Data](https://docs.pixeltable.com/docs/datastore/sampling.md): Learn how to create representative samples from your data for analysis, testing, and machine learning.
- [Tables](https://docs.pixeltable.com/docs/datastore/tables-and-operations.md): Learn the fundamentals of Pixeltable tables, types, and how to build in Pixeltable
- [Vector Database](https://docs.pixeltable.com/docs/datastore/vector-database.md): Learn how to create, populate and query embedding indexes in Pixeltable
- [Views](https://docs.pixeltable.com/docs/datastore/views.md): Learn how to create and use virtual derived tables in Pixeltable through views
- [Evaluations](https://docs.pixeltable.com/docs/examples/chat/evals.md): Add automated quality assessment to your AI applications using LLM-based evaluation
- [Memory](https://docs.pixeltable.com/docs/examples/chat/memory.md): Build a chatbot that remembers conversation history using Pixeltable
- [Multimodal Chatbot](https://docs.pixeltable.com/docs/examples/chat/multimodal.md): Build a chat application that processes documents, videos, and audio
- [Tools](https://docs.pixeltable.com/docs/examples/chat/tools.md): Build AI agents that can invoke custom tools
- [Interactive Demos](https://docs.pixeltable.com/docs/examples/interactive-demos.md): Explore Pixeltable capabilities through interactive demos
- [Sample Applications](https://docs.pixeltable.com/docs/examples/sample-apps.md): Explore real-world applications built with Pixeltable
- [PDF](https://docs.pixeltable.com/docs/examples/search/PDF.md): Build a PDF search system using smart chunking and vector embeddings
- [Audio](https://docs.pixeltable.com/docs/examples/search/audio.md): Build an audio-to-text knowledge base with search capabilities
- [Image](https://docs.pixeltable.com/docs/examples/search/images.md): Build an image search system using OpenAI Vision and vector embeddings
- [Video](https://docs.pixeltable.com/docs/examples/search/video.md): Build a multimodal video search workflow with Pixeltable
- [Website](https://docs.pixeltable.com/docs/examples/search/website.md): Build a web content search system using smart chunking and vector embeddings
- [Use Cases](https://docs.pixeltable.com/docs/examples/use-cases.md): Explore practical applications and implementation patterns with Pixeltable
- [Label Studio](https://docs.pixeltable.com/docs/examples/vision/label-studio.md): Build annotation workflows with Pixeltable and Label Studio in two phases
- [Voxel51](https://docs.pixeltable.com/docs/examples/vision/voxel51.md): Build image analysis workflows with Pixeltable and Voxel51 in two phases
- [YOLOX Object Detection](https://docs.pixeltable.com/docs/examples/vision/yolox.md): Use YOLOX object detection in Pixeltable by defining your schema, then using it
- [Embedding Models](https://docs.pixeltable.com/docs/integrations/embedding-model.md): Learn how to integrate custom embedding models with Pixeltable
- [Ecosystem](https://docs.pixeltable.com/docs/integrations/frameworks.md): Explore Pixeltable ecosystem of built-in integrations for AI/ML workflows
- [Model Hub & Repositories](https://docs.pixeltable.com/docs/integrations/models.md): Explore pre-trained models and integrations available in Pixeltable
- [Multimodal MCP Servers](https://docs.pixeltable.com/docs/libraries/mcp.md): Extending AI capabilities with Pixeltable MCP servers
- [Pixelagent](https://docs.pixeltable.com/docs/libraries/pixelagent.md): An Agent Engineering Blueprint powered by Pixeltable
- [Pixeltable YOLOX](https://docs.pixeltable.com/docs/libraries/yolox.md): Lightweight object detection library built on PyTorch
- [Building with LLMs](https://docs.pixeltable.com/docs/overview/building-pixeltable-with-llms.md): Use LLMs in your Pixeltable integration workflow
- [Configuration](https://docs.pixeltable.com/docs/overview/configuration.md): Complete guide to configuring Pixeltable
- [Installation](https://docs.pixeltable.com/docs/overview/installation.md): Complete guide to installing and setting up Pixeltable
- [Introduction](https://docs.pixeltable.com/docs/overview/pixeltable.md): Pixeltable is a declarative data infrastructure for building multimodal AI applications, enabling incremental storage, transformation, indexing, and orchestration of data.
- [Quick Start](https://docs.pixeltable.com/docs/overview/quick-start.md): Welcome to Pixeltable! In this tutorial, we will survey how to create tables, populate them with data, and enhance them with built-in and user-defined transformations and AI operations.
- [FAQ](https://docs.pixeltable.com/docs/support/faq.md): Frequently asked questions about Pixeltable
- [Getting Help](https://docs.pixeltable.com/docs/support/getting-help.md): Connect with the Pixeltable community and find support
- [Feature Guides](https://docs.pixeltable.com/docs/tutorials/feature-guide.md): Deep dive into Pixeltable advanced capabilities and patterns
- [Fundamentals](https://docs.pixeltable.com/docs/tutorials/fundamentals.md): Learn Pixeltable core concepts and capabilities through interactive tutorials


## Optional

- [API Reference](https://pixeltable.github.io/pixeltable/)